'use client';

import { useState } from 'react';
import { useCreatePatient } from '@/hooks/usePatients';
import { Patient } from '@/types';
import { 
  UserIcon, 
  PhoneIcon, 
  EnvelopeIcon, 
  MapPinIcon,
  IdentificationIcon,
  CalendarDaysIcon,
  PhotoIcon
} from '@heroicons/react/24/outline';

interface PatientRegistrationFormProps {
  onSuccess?: (patientId: string) => void;
  onCancel?: () => void;
}

export default function PatientRegistrationForm({ 
  onSuccess, 
  onCancel 
}: PatientRegistrationFormProps) {
  const createPatientMutation = useCreatePatient();
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    address: '',
    nik: '',
    gender: 'male' as 'male' | 'female',
    avatar: '',
    emergencyContact: {
      name: '',
      phone: '',
      relationship: ''
    },
    medicalHistory: {
      allergies: [] as string[],
      medications: [] as string[],
      conditions: [] as string[]
    }
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [allergiesInput, setAllergiesInput] = useState('');
  const [medicationsInput, setMedicationsInput] = useState('');
  const [conditionsInput, setConditionsInput] = useState('');

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = 'Nama wajib diisi';
    if (!formData.phone.trim()) newErrors.phone = 'Nomor telepon wajib diisi';
    if (!formData.dateOfBirth) newErrors.dateOfBirth = 'Tanggal lahir wajib diisi';
    if (!formData.address.trim()) newErrors.address = 'Alamat wajib diisi';
    if (!formData.nik.trim()) newErrors.nik = 'NIK wajib diisi';
    if (formData.nik.length !== 16) newErrors.nik = 'NIK harus 16 digit';
    if (!formData.emergencyContact.name.trim()) newErrors.emergencyContactName = 'Nama kontak darurat wajib diisi';
    if (!formData.emergencyContact.phone.trim()) newErrors.emergencyContactPhone = 'Telepon kontak darurat wajib diisi';
    if (!formData.emergencyContact.relationship.trim()) newErrors.emergencyContactRelationship = 'Hubungan kontak darurat wajib diisi';

    // Email validation (optional but must be valid if provided)
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Format email tidak valid';
    }

    // Phone validation
    if (!/^[0-9+\-\s()]+$/.test(formData.phone)) {
      newErrors.phone = 'Format nomor telepon tidak valid';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleEmergencyContactChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      emergencyContact: {
        ...prev.emergencyContact,
        [field]: value
      }
    }));
    
    // Clear error when user starts typing
    const errorKey = `emergencyContact${field.charAt(0).toUpperCase() + field.slice(1)}`;
    if (errors[errorKey]) {
      setErrors(prev => ({
        ...prev,
        [errorKey]: ''
      }));
    }
  };

  const handleArrayInput = (type: 'allergies' | 'medications' | 'conditions', value: string) => {
    const items = value.split(',').map(item => item.trim()).filter(item => item);
    setFormData(prev => ({
      ...prev,
      medicalHistory: {
        ...prev.medicalHistory,
        [type]: items
      }
    }));
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setFormData(prev => ({
          ...prev,
          avatar: result
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    
    try {
      const patientData: Omit<Patient, 'id' | 'medicalRecordNumber'> = {
        ...formData,
        totalVisits: 0,
        status: 'active',
        dentalChart: [],
        clinicalImages: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const patientId = await createPatientMutation.mutateAsync(patientData);
      onSuccess?.(patientId);
    } catch (error) {
      console.error('Error creating patient:', error);
      setErrors({ submit: 'Gagal menyimpan data pasien. Silakan coba lagi.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Header */}
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900">Registrasi Pasien Baru</h2>
          <p className="mt-2 text-gray-600">Lengkapi data pasien dengan teliti</p>
        </div>

        {/* Personal Information */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
            <UserIcon className="w-5 h-5 mr-2" />
            Informasi Personal
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Avatar Upload */}
            <div className="md:col-span-2 flex justify-center">
              <div className="relative">
                <div className="w-24 h-24 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                  {formData.avatar ? (
                    <img 
                      src={formData.avatar} 
                      alt="Avatar" 
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <PhotoIcon className="w-8 h-8 text-gray-400" />
                  )}
                </div>
                <label className="absolute bottom-0 right-0 bg-primary-600 text-white p-1 rounded-full cursor-pointer hover:bg-primary-700">
                  <PhotoIcon className="w-4 h-4" />
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                  />
                </label>
              </div>
            </div>

            {/* Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nama Lengkap *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={`input ${errors.name ? 'border-red-500' : ''}`}
                placeholder="Masukkan nama lengkap"
              />
              {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
            </div>

            {/* Email */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email
              </label>
              <div className="relative">
                <EnvelopeIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className={`input pl-10 ${errors.email ? 'border-red-500' : ''}`}
                  placeholder="<EMAIL>"
                />
              </div>
              {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email}</p>}
            </div>

            {/* Phone */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nomor Telepon *
              </label>
              <div className="relative">
                <PhoneIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className={`input pl-10 ${errors.phone ? 'border-red-500' : ''}`}
                  placeholder="08123456789"
                />
              </div>
              {errors.phone && <p className="mt-1 text-sm text-red-600">{errors.phone}</p>}
            </div>

            {/* Date of Birth */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tanggal Lahir *
              </label>
              <div className="relative">
                <CalendarDaysIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="date"
                  value={formData.dateOfBirth}
                  onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                  className={`input pl-10 ${errors.dateOfBirth ? 'border-red-500' : ''}`}
                />
              </div>
              {errors.dateOfBirth && <p className="mt-1 text-sm text-red-600">{errors.dateOfBirth}</p>}
            </div>

            {/* Gender */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Jenis Kelamin *
              </label>
              <select
                value={formData.gender}
                onChange={(e) => handleInputChange('gender', e.target.value)}
                className="input"
              >
                <option value="male">Laki-laki</option>
                <option value="female">Perempuan</option>
              </select>
            </div>

            {/* NIK */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                NIK *
              </label>
              <div className="relative">
                <IdentificationIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  value={formData.nik}
                  onChange={(e) => handleInputChange('nik', e.target.value)}
                  className={`input pl-10 ${errors.nik ? 'border-red-500' : ''}`}
                  placeholder="16 digit NIK"
                  maxLength={16}
                />
              </div>
              {errors.nik && <p className="mt-1 text-sm text-red-600">{errors.nik}</p>}
            </div>

            {/* Address */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Alamat *
              </label>
              <div className="relative">
                <MapPinIcon className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                <textarea
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  className={`input pl-10 min-h-[80px] ${errors.address ? 'border-red-500' : ''}`}
                  placeholder="Alamat lengkap"
                  rows={3}
                />
              </div>
              {errors.address && <p className="mt-1 text-sm text-red-600">{errors.address}</p>}
            </div>
          </div>
        </div>

        {/* Emergency Contact */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
            <PhoneIcon className="w-5 h-5 mr-2" />
            Kontak Darurat
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nama *
              </label>
              <input
                type="text"
                value={formData.emergencyContact.name}
                onChange={(e) => handleEmergencyContactChange('name', e.target.value)}
                className={`input ${errors.emergencyContactName ? 'border-red-500' : ''}`}
                placeholder="Nama kontak darurat"
              />
              {errors.emergencyContactName && <p className="mt-1 text-sm text-red-600">{errors.emergencyContactName}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nomor Telepon *
              </label>
              <input
                type="tel"
                value={formData.emergencyContact.phone}
                onChange={(e) => handleEmergencyContactChange('phone', e.target.value)}
                className={`input ${errors.emergencyContactPhone ? 'border-red-500' : ''}`}
                placeholder="08123456789"
              />
              {errors.emergencyContactPhone && <p className="mt-1 text-sm text-red-600">{errors.emergencyContactPhone}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Hubungan *
              </label>
              <select
                value={formData.emergencyContact.relationship}
                onChange={(e) => handleEmergencyContactChange('relationship', e.target.value)}
                className={`input ${errors.emergencyContactRelationship ? 'border-red-500' : ''}`}
              >
                <option value="">Pilih hubungan</option>
                <option value="spouse">Suami/Istri</option>
                <option value="parent">Orang Tua</option>
                <option value="child">Anak</option>
                <option value="sibling">Saudara</option>
                <option value="friend">Teman</option>
                <option value="other">Lainnya</option>
              </select>
              {errors.emergencyContactRelationship && <p className="mt-1 text-sm text-red-600">{errors.emergencyContactRelationship}</p>}
            </div>
          </div>
        </div>

        {/* Medical History */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">
            Riwayat Kesehatan
          </h3>
          
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Alergi (pisahkan dengan koma)
              </label>
              <input
                type="text"
                value={allergiesInput}
                onChange={(e) => {
                  setAllergiesInput(e.target.value);
                  handleArrayInput('allergies', e.target.value);
                }}
                className="input"
                placeholder="Contoh: Penisilin, Seafood, Kacang"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Obat yang Sedang Dikonsumsi (pisahkan dengan koma)
              </label>
              <input
                type="text"
                value={medicationsInput}
                onChange={(e) => {
                  setMedicationsInput(e.target.value);
                  handleArrayInput('medications', e.target.value);
                }}
                className="input"
                placeholder="Contoh: Aspirin, Vitamin D, Insulin"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Kondisi Medis (pisahkan dengan koma)
              </label>
              <input
                type="text"
                value={conditionsInput}
                onChange={(e) => {
                  setConditionsInput(e.target.value);
                  handleArrayInput('conditions', e.target.value);
                }}
                className="input"
                placeholder="Contoh: Diabetes, Hipertensi, Jantung"
              />
            </div>
          </div>
        </div>

        {/* Submit Error */}
        {errors.submit && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-600">{errors.submit}</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="btn-secondary"
              disabled={isSubmitting}
            >
              Batal
            </button>
          )}
          <button
            type="submit"
            className="btn-primary"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Menyimpan...' : 'Simpan Pasien'}
          </button>
        </div>
      </form>
    </div>
  );
}
