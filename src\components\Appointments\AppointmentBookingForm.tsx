'use client';

import { useState, useEffect } from 'react';
import { useCreateAppointment } from '@/hooks/useAppointments';
import { usePatients } from '@/hooks/usePatients';
import { useTreatments } from '@/hooks/useTreatments';
import { Appointment } from '@/types';
import { 
  CalendarDaysIcon,
  ClockIcon,
  UserIcon,
  DocumentTextIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

interface AppointmentBookingFormProps {
  onSuccess?: (appointmentId: string) => void;
  onCancel?: () => void;
  selectedDate?: string;
  selectedTime?: string;
}

export default function AppointmentBookingForm({ 
  onSuccess, 
  onCancel,
  selectedDate,
  selectedTime 
}: AppointmentBookingFormProps) {
  const createAppointmentMutation = useCreateAppointment();
  const { data: patients = [] } = usePatients();
  const { data: treatments = [] } = useTreatments();
  
  const [formData, setFormData] = useState({
    patientId: '',
    patientName: '',
    doctorName: 'Dr. <PERSON><PERSON>', // Default doctor
    date: selectedDate || new Date().toISOString().split('T')[0],
    time: selectedTime || '09:00',
    duration: 30,
    type: '',
    treatmentId: '',
    notes: '',
    status: 'scheduled' as const
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showNewPatientForm, setShowNewPatientForm] = useState(false);

  // Available time slots
  const timeSlots = [
    '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
    '13:00', '13:30', '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00'
  ];

  // Available doctors
  const doctors = [
    'Dr. Sari Wijaya',
    'Dr. Budi Santoso',
    'Dr. Maya Putri',
    'Dr. Ahmad Rahman'
  ];

  // Update patient name when patient is selected
  useEffect(() => {
    if (formData.patientId) {
      const selectedPatient = patients.find(p => p.id === formData.patientId);
      if (selectedPatient) {
        setFormData(prev => ({
          ...prev,
          patientName: selectedPatient.name
        }));
      }
    }
  }, [formData.patientId, patients]);

  // Update duration when treatment is selected
  useEffect(() => {
    if (formData.treatmentId) {
      const selectedTreatment = treatments.find(t => t.id === formData.treatmentId);
      if (selectedTreatment) {
        setFormData(prev => ({
          ...prev,
          type: selectedTreatment.name,
          duration: selectedTreatment.estimatedDuration || 30
        }));
      }
    }
  }, [formData.treatmentId, treatments]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.patientId && !showNewPatientForm) {
      newErrors.patientId = 'Pilih pasien atau tambah pasien baru';
    }
    if (!formData.patientName.trim()) {
      newErrors.patientName = 'Nama pasien wajib diisi';
    }
    if (!formData.doctorName) {
      newErrors.doctorName = 'Pilih dokter';
    }
    if (!formData.date) {
      newErrors.date = 'Tanggal appointment wajib diisi';
    }
    if (!formData.time) {
      newErrors.time = 'Waktu appointment wajib diisi';
    }
    if (!formData.type.trim()) {
      newErrors.type = 'Jenis treatment wajib diisi';
    }
    if (formData.duration < 15 || formData.duration > 180) {
      newErrors.duration = 'Durasi harus antara 15-180 menit';
    }

    // Check if appointment time is in the past
    const appointmentDateTime = new Date(`${formData.date}T${formData.time}`);
    if (appointmentDateTime < new Date()) {
      newErrors.datetime = 'Tidak dapat membuat appointment di waktu yang sudah lewat';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    
    try {
      const appointmentData: Omit<Appointment, 'id'> = {
        patientId: formData.patientId,
        patientName: formData.patientName,
        doctorName: formData.doctorName,
        date: formData.date,
        time: formData.time,
        duration: formData.duration,
        type: formData.type,
        notes: formData.notes,
        status: formData.status,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const appointmentId = await createAppointmentMutation.mutateAsync(appointmentData);
      onSuccess?.(appointmentId);
    } catch (error) {
      console.error('Error creating appointment:', error);
      setErrors({ submit: 'Gagal membuat appointment. Silakan coba lagi.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Buat Appointment Baru</h2>
            <p className="mt-1 text-gray-600">Isi detail appointment dengan lengkap</p>
          </div>
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="p-2 hover:bg-gray-100 rounded-lg"
            >
              <XMarkIcon className="w-6 h-6 text-gray-400" />
            </button>
          )}
        </div>

        {/* Patient Selection */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <UserIcon className="w-5 h-5 mr-2" />
            Informasi Pasien
          </h3>
          
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <button
                type="button"
                onClick={() => setShowNewPatientForm(false)}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  !showNewPatientForm 
                    ? 'bg-primary-100 text-primary-700' 
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                Pilih Pasien Existing
              </button>
              <button
                type="button"
                onClick={() => setShowNewPatientForm(true)}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  showNewPatientForm 
                    ? 'bg-primary-100 text-primary-700' 
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                Pasien Baru
              </button>
            </div>

            {!showNewPatientForm ? (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Pilih Pasien *
                </label>
                <select
                  value={formData.patientId}
                  onChange={(e) => handleInputChange('patientId', e.target.value)}
                  className={`input ${errors.patientId ? 'border-red-500' : ''}`}
                >
                  <option value="">Pilih pasien...</option>
                  {patients.map((patient) => (
                    <option key={patient.id} value={patient.id}>
                      {patient.name} - {patient.medicalRecordNumber}
                    </option>
                  ))}
                </select>
                {errors.patientId && <p className="mt-1 text-sm text-red-600">{errors.patientId}</p>}
              </div>
            ) : (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nama Pasien *
                </label>
                <input
                  type="text"
                  value={formData.patientName}
                  onChange={(e) => handleInputChange('patientName', e.target.value)}
                  className={`input ${errors.patientName ? 'border-red-500' : ''}`}
                  placeholder="Masukkan nama pasien baru"
                />
                {errors.patientName && <p className="mt-1 text-sm text-red-600">{errors.patientName}</p>}
                <p className="mt-1 text-sm text-gray-500">
                  Pasien baru akan didaftarkan otomatis setelah appointment dibuat
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Appointment Details */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <CalendarDaysIcon className="w-5 h-5 mr-2" />
            Detail Appointment
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tanggal *
              </label>
              <input
                type="date"
                value={formData.date}
                onChange={(e) => handleInputChange('date', e.target.value)}
                className={`input ${errors.date ? 'border-red-500' : ''}`}
                min={new Date().toISOString().split('T')[0]}
              />
              {errors.date && <p className="mt-1 text-sm text-red-600">{errors.date}</p>}
            </div>

            {/* Time */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Waktu *
              </label>
              <select
                value={formData.time}
                onChange={(e) => handleInputChange('time', e.target.value)}
                className={`input ${errors.time ? 'border-red-500' : ''}`}
              >
                {timeSlots.map((time) => (
                  <option key={time} value={time}>
                    {time}
                  </option>
                ))}
              </select>
              {errors.time && <p className="mt-1 text-sm text-red-600">{errors.time}</p>}
            </div>

            {/* Doctor */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Dokter *
              </label>
              <select
                value={formData.doctorName}
                onChange={(e) => handleInputChange('doctorName', e.target.value)}
                className={`input ${errors.doctorName ? 'border-red-500' : ''}`}
              >
                {doctors.map((doctor) => (
                  <option key={doctor} value={doctor}>
                    {doctor}
                  </option>
                ))}
              </select>
              {errors.doctorName && <p className="mt-1 text-sm text-red-600">{errors.doctorName}</p>}
            </div>

            {/* Duration */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Durasi (menit) *
              </label>
              <input
                type="number"
                value={formData.duration}
                onChange={(e) => handleInputChange('duration', parseInt(e.target.value))}
                className={`input ${errors.duration ? 'border-red-500' : ''}`}
                min="15"
                max="180"
                step="15"
              />
              {errors.duration && <p className="mt-1 text-sm text-red-600">{errors.duration}</p>}
            </div>

            {/* Treatment Type */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Jenis Treatment *
              </label>
              <div className="flex space-x-2">
                <select
                  value={formData.treatmentId}
                  onChange={(e) => handleInputChange('treatmentId', e.target.value)}
                  className="input flex-1"
                >
                  <option value="">Pilih treatment...</option>
                  {treatments.map((treatment) => (
                    <option key={treatment.id} value={treatment.id}>
                      {treatment.name} - {treatment.price ? `Rp ${treatment.price.toLocaleString()}` : ''}
                    </option>
                  ))}
                </select>
                <input
                  type="text"
                  value={formData.type}
                  onChange={(e) => handleInputChange('type', e.target.value)}
                  className={`input flex-1 ${errors.type ? 'border-red-500' : ''}`}
                  placeholder="Atau ketik manual"
                />
              </div>
              {errors.type && <p className="mt-1 text-sm text-red-600">{errors.type}</p>}
            </div>
          </div>
        </div>

        {/* Notes */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <DocumentTextIcon className="w-5 h-5 mr-2" />
            Catatan Tambahan
          </h3>
          
          <textarea
            value={formData.notes}
            onChange={(e) => handleInputChange('notes', e.target.value)}
            className="input min-h-[100px]"
            placeholder="Catatan khusus untuk appointment ini..."
            rows={4}
          />
        </div>

        {/* Submit Error */}
        {errors.submit && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-600">{errors.submit}</p>
          </div>
        )}

        {/* DateTime Error */}
        {errors.datetime && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-600">{errors.datetime}</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="btn-secondary"
              disabled={isSubmitting}
            >
              Batal
            </button>
          )}
          <button
            type="submit"
            className="btn-primary"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Menyimpan...' : 'Buat Appointment'}
          </button>
        </div>
      </form>
    </div>
  );
}
