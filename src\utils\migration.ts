import { 
  collection, 
  doc, 
  getDocs, 
  getDoc,
  setDoc, 
  writeBatch,
  query,
  where
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

/**
 * Migration utility to convert single-tenant structure to multi-tenant
 * This should be run once to migrate existing data
 */
export class DataMigration {
  
  /**
   * Migrate all existing data to the new multi-tenant structure
   */
  static async migrateToMultiTenant(): Promise<void> {
    console.log('Starting multi-tenant migration...');
    
    try {
      // Step 1: Migrate users and create tenant mappings
      const tenantMappings = await this.migrateUsers();
      
      // Step 2: Migrate patients
      await this.migratePatients(tenantMappings);
      
      // Step 3: Migrate appointments
      await this.migrateAppointments(tenantMappings);
      
      // Step 4: Migrate treatments
      await this.migrateTreatments(tenantMappings);
      
      // Step 5: Migrate inventory
      await this.migrateInventory(tenantMappings);
      
      // Step 6: Migrate clinic settings
      await this.migrateClinicSettings(tenantMappings);
      
      console.log('Multi-tenant migration completed successfully!');
    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  }

  /**
   * Migrate users and create tenant mappings
   */
  private static async migrateUsers(): Promise<Map<string, string>> {
    console.log('Migrating users...');
    const tenantMappings = new Map<string, string>(); // clinicId -> tenantId
    
    const usersSnapshot = await getDocs(collection(db, 'users'));
    const batch = writeBatch(db);
    
    for (const userDoc of usersSnapshot.docs) {
      const userData = userDoc.data();
      const oldClinicId = userData.clinicId;
      
      // Generate or get tenant ID for this clinic
      let tenantId = tenantMappings.get(oldClinicId);
      if (!tenantId) {
        tenantId = `tenant_${oldClinicId}_${Date.now()}`;
        tenantMappings.set(oldClinicId, tenantId);
      }
      
      // Update user document with tenantId
      const updatedUserData: any = {
        ...userData,
        tenantId,
        updatedAt: new Date().toISOString()
      };
      delete updatedUserData.clinicId; // Remove old clinicId
      
      // Update global user document
      batch.set(doc(db, 'users', userDoc.id), updatedUserData, { merge: true });
      
      // Create tenant-specific user document
      batch.set(
        doc(db, 'dentalcare', tenantId, 'users', userDoc.id),
        updatedUserData
      );
    }
    
    await batch.commit();
    console.log(`Migrated ${usersSnapshot.docs.length} users`);
    return tenantMappings;
  }

  /**
   * Migrate patients to tenant-specific collections
   */
  private static async migratePatients(tenantMappings: Map<string, string>): Promise<void> {
    console.log('Migrating patients...');
    
    const patientsSnapshot = await getDocs(collection(db, 'patients'));
    const batch = writeBatch(db);
    let batchCount = 0;
    
    for (const patientDoc of patientsSnapshot.docs) {
      const patientData = patientDoc.data();
      const oldClinicId = patientData.clinicId;
      const tenantId = tenantMappings.get(oldClinicId);
      
      if (!tenantId) {
        console.warn(`No tenant mapping found for clinicId: ${oldClinicId}`);
        continue;
      }
      
      // Remove clinicId and update timestamps
      const updatedPatientData: any = {
        ...patientData,
        updatedAt: new Date().toISOString()
      };
      delete updatedPatientData.clinicId;
      
      // Create patient in tenant-specific collection
      batch.set(
        doc(db, 'dentalcare', tenantId, 'patients', patientDoc.id),
        updatedPatientData
      );
      
      batchCount++;
      
      // Commit batch every 500 operations (Firestore limit)
      if (batchCount >= 500) {
        await batch.commit();
        console.log(`Committed batch of ${batchCount} patients`);
        batchCount = 0;
      }
    }
    
    if (batchCount > 0) {
      await batch.commit();
    }
    
    console.log(`Migrated ${patientsSnapshot.docs.length} patients`);
  }

  /**
   * Migrate appointments to tenant-specific collections
   */
  private static async migrateAppointments(tenantMappings: Map<string, string>): Promise<void> {
    console.log('Migrating appointments...');
    
    const appointmentsSnapshot = await getDocs(collection(db, 'appointments'));
    const batch = writeBatch(db);
    let batchCount = 0;
    
    for (const appointmentDoc of appointmentsSnapshot.docs) {
      const appointmentData = appointmentDoc.data();
      const oldClinicId = appointmentData.clinicId;
      const tenantId = tenantMappings.get(oldClinicId);
      
      if (!tenantId) {
        console.warn(`No tenant mapping found for clinicId: ${oldClinicId}`);
        continue;
      }
      
      // Remove clinicId and update timestamps
      const updatedAppointmentData: any = {
        ...appointmentData,
        updatedAt: new Date().toISOString()
      };
      delete updatedAppointmentData.clinicId;
      
      // Create appointment in tenant-specific collection
      batch.set(
        doc(db, 'dentalcare', tenantId, 'appointments', appointmentDoc.id),
        updatedAppointmentData
      );
      
      batchCount++;
      
      if (batchCount >= 500) {
        await batch.commit();
        console.log(`Committed batch of ${batchCount} appointments`);
        batchCount = 0;
      }
    }
    
    if (batchCount > 0) {
      await batch.commit();
    }
    
    console.log(`Migrated ${appointmentsSnapshot.docs.length} appointments`);
  }

  /**
   * Migrate treatments to tenant-specific collections
   */
  private static async migrateTreatments(tenantMappings: Map<string, string>): Promise<void> {
    console.log('Migrating treatments...');
    
    const treatmentsSnapshot = await getDocs(collection(db, 'treatments'));
    
    // Treatments might be shared across tenants, so we'll copy them to each tenant
    for (const [clinicId, tenantId] of Array.from(tenantMappings.entries())) {
      const batch = writeBatch(db);
      
      for (const treatmentDoc of treatmentsSnapshot.docs) {
        const treatmentData = treatmentDoc.data();
        
        batch.set(
          doc(db, 'dentalcare', tenantId, 'treatments', treatmentDoc.id),
          {
            ...treatmentData,
            updatedAt: new Date().toISOString()
          }
        );
      }
      
      await batch.commit();
    }
    
    console.log(`Migrated treatments to ${tenantMappings.size} tenants`);
  }

  /**
   * Migrate inventory to tenant-specific collections
   */
  private static async migrateInventory(tenantMappings: Map<string, string>): Promise<void> {
    console.log('Migrating inventory...');
    
    const inventorySnapshot = await getDocs(collection(db, 'inventory'));
    
    for (const [clinicId, tenantId] of Array.from(tenantMappings.entries())) {
      const batch = writeBatch(db);
      
      for (const inventoryDoc of inventorySnapshot.docs) {
        const inventoryData = inventoryDoc.data();
        
        batch.set(
          doc(db, 'dentalcare', tenantId, 'inventory', inventoryDoc.id),
          {
            ...inventoryData,
            updatedAt: new Date().toISOString()
          }
        );
      }
      
      await batch.commit();
    }
    
    console.log(`Migrated inventory to ${tenantMappings.size} tenants`);
  }

  /**
   * Migrate clinic settings
   */
  private static async migrateClinicSettings(tenantMappings: Map<string, string>): Promise<void> {
    console.log('Migrating clinic settings...');
    
    const clinicsSnapshot = await getDocs(collection(db, 'clinics'));
    
    for (const clinicDoc of clinicsSnapshot.docs) {
      const clinicData = clinicDoc.data();
      const tenantId = tenantMappings.get(clinicDoc.id);
      
      if (!tenantId) {
        console.warn(`No tenant mapping found for clinic: ${clinicDoc.id}`);
        continue;
      }
      
      // Create tenant settings
      await setDoc(
        doc(db, 'dentalcare', tenantId, 'settings', 'clinic'),
        {
          id: tenantId,
          ...clinicData,
          updatedAt: new Date().toISOString()
        }
      );
    }
    
    console.log(`Migrated ${clinicsSnapshot.docs.length} clinic settings`);
  }

  /**
   * Rollback migration (use with caution!)
   */
  static async rollbackMigration(): Promise<void> {
    console.warn('Rolling back migration - this will delete all tenant-specific data!');
    
    // This is a destructive operation - implement with extreme caution
    // For now, just log a warning
    console.warn('Rollback not implemented - manual cleanup required');
  }
}
