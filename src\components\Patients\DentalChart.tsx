'use client';

import { useState } from 'react';
import { Patient } from '@/types';

interface DentalChartProps {
  patient: Patient;
  onToothUpdate?: (toothNumber: number, condition: string, notes?: string) => void;
  readOnly?: boolean;
}

type ToothCondition = 'healthy' | 'caries' | 'filled' | 'crown' | 'missing' | 'root_canal';

const conditionColors: Record<ToothCondition, string> = {
  healthy: 'fill-white stroke-gray-400',
  caries: 'fill-red-200 stroke-red-400',
  filled: 'fill-blue-200 stroke-blue-400',
  crown: 'fill-yellow-200 stroke-yellow-400',
  missing: 'fill-gray-300 stroke-gray-500',
  root_canal: 'fill-purple-200 stroke-purple-400'
};

const conditionLabels: Record<ToothCondition, string> = {
  healthy: 'Sehat',
  caries: 'Karies',
  filled: 'Tambal',
  crown: 'Crown',
  missing: 'Hilang',
  root_canal: 'Perawatan Saluran Akar'
};

export default function DentalChart({ patient, onToothUpdate, readOnly = false }: DentalChartProps) {
  const [selectedTooth, setSelectedTooth] = useState<number | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [modalCondition, setModalCondition] = useState<ToothCondition>('healthy');
  const [modalNotes, setModalNotes] = useState('');

  // Initialize dental chart if not exists
  const dentalChart = patient.dentalChart || Array.from({ length: 32 }, (_, i) => ({
    toothNumber: i + 1,
    condition: 'healthy' as const,
    notes: '',
    updatedAt: new Date().toISOString()
  }));

  const getToothCondition = (toothNumber: number): ToothCondition => {
    const tooth = dentalChart.find(t => t.toothNumber === toothNumber);
    return (tooth?.condition as ToothCondition) || 'healthy';
  };

  const getToothNotes = (toothNumber: number): string => {
    const tooth = dentalChart.find(t => t.toothNumber === toothNumber);
    return tooth?.notes || '';
  };

  const handleToothClick = (toothNumber: number) => {
    if (readOnly) return;
    
    setSelectedTooth(toothNumber);
    setModalCondition(getToothCondition(toothNumber));
    setModalNotes(getToothNotes(toothNumber));
    setShowModal(true);
  };

  const handleSaveToothCondition = () => {
    if (selectedTooth && onToothUpdate) {
      onToothUpdate(selectedTooth, modalCondition, modalNotes);
    }
    setShowModal(false);
    setSelectedTooth(null);
  };

  // Tooth numbering: 1-16 upper jaw, 17-32 lower jaw
  const upperTeeth = Array.from({ length: 16 }, (_, i) => i + 1);
  const lowerTeeth = Array.from({ length: 16 }, (_, i) => i + 17);

  const ToothSVG = ({ toothNumber, condition }: { toothNumber: number; condition: ToothCondition }) => (
    <g
      className={`cursor-pointer transition-all duration-200 hover:opacity-80 ${
        selectedTooth === toothNumber ? 'ring-2 ring-primary-500' : ''
      }`}
      onClick={() => handleToothClick(toothNumber)}
    >
      {/* Tooth shape - simplified rectangle for now */}
      <rect
        x="0"
        y="0"
        width="24"
        height="32"
        rx="4"
        className={conditionColors[condition]}
        strokeWidth="2"
      />
      {/* Tooth number */}
      <text
        x="12"
        y="20"
        textAnchor="middle"
        className="text-xs font-medium fill-gray-700"
        style={{ fontSize: '10px' }}
      >
        {toothNumber}
      </text>
    </g>
  );

  return (
    <div className="space-y-6">
      {/* Legend */}
      <div className="flex flex-wrap gap-4 p-4 bg-gray-50 rounded-lg">
        <h4 className="w-full text-sm font-medium text-gray-700 mb-2">Keterangan:</h4>
        {Object.entries(conditionLabels).map(([condition, label]) => (
          <div key={condition} className="flex items-center space-x-2">
            <div className={`w-4 h-4 rounded border-2 ${conditionColors[condition as ToothCondition]}`}></div>
            <span className="text-sm text-gray-600">{label}</span>
          </div>
        ))}
      </div>

      {/* Dental Chart */}
      <div className="bg-white border rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Dental Chart</h3>
        
        <div className="space-y-8">
          {/* Upper Jaw */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-3">Rahang Atas</h4>
            <div className="flex justify-center">
              <svg width="450" height="50" viewBox="0 0 450 50" className="border rounded">
                {upperTeeth.map((toothNumber, index) => (
                  <g key={toothNumber} transform={`translate(${index * 28 + 5}, 9)`}>
                    <ToothSVG 
                      toothNumber={toothNumber} 
                      condition={getToothCondition(toothNumber)} 
                    />
                  </g>
                ))}
              </svg>
            </div>
          </div>

          {/* Lower Jaw */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-3">Rahang Bawah</h4>
            <div className="flex justify-center">
              <svg width="450" height="50" viewBox="0 0 450 50" className="border rounded">
                {lowerTeeth.map((toothNumber, index) => (
                  <g key={toothNumber} transform={`translate(${index * 28 + 5}, 9)`}>
                    <ToothSVG 
                      toothNumber={toothNumber} 
                      condition={getToothCondition(toothNumber)} 
                    />
                  </g>
                ))}
              </svg>
            </div>
          </div>
        </div>

        {!readOnly && (
          <p className="text-sm text-gray-500 mt-4 text-center">
            Klik pada gigi untuk mengubah kondisi
          </p>
        )}
      </div>

      {/* Tooth Condition Modal */}
      {showModal && selectedTooth && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Gigi #{selectedTooth}
              </h3>
              <button 
                onClick={() => setShowModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Kondisi Gigi
                </label>
                <select
                  value={modalCondition}
                  onChange={(e) => setModalCondition(e.target.value as ToothCondition)}
                  className="input"
                >
                  {Object.entries(conditionLabels).map(([condition, label]) => (
                    <option key={condition} value={condition}>
                      {label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Catatan
                </label>
                <textarea
                  value={modalNotes}
                  onChange={(e) => setModalNotes(e.target.value)}
                  className="input min-h-[80px]"
                  placeholder="Catatan tambahan untuk gigi ini..."
                  rows={3}
                />
              </div>

              {/* Current condition preview */}
              <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                <div className={`w-6 h-6 rounded border-2 ${conditionColors[modalCondition]}`}></div>
                <span className="text-sm font-medium text-gray-700">
                  {conditionLabels[modalCondition]}
                </span>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowModal(false)}
                className="btn-secondary"
              >
                Batal
              </button>
              <button
                onClick={handleSaveToothCondition}
                className="btn-primary"
              >
                Simpan
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Dental Chart Summary */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        {Object.entries(conditionLabels).map(([condition, label]) => {
          const count = dentalChart.filter(tooth => tooth.condition === condition).length;
          return (
            <div key={condition} className="text-center p-3 bg-gray-50 rounded-lg">
              <div className={`w-8 h-8 mx-auto mb-2 rounded border-2 ${conditionColors[condition as ToothCondition]}`}></div>
              <p className="text-sm font-medium text-gray-900">{count}</p>
              <p className="text-xs text-gray-600">{label}</p>
            </div>
          );
        })}
      </div>
    </div>
  );
}
