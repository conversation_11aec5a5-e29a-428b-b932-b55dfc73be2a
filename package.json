{"name": "dental-clinic-ui", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "migrate": "node src/scripts/runMigration.js migrate", "migrate:check": "node src/scripts/runMigration.js check", "deploy:rules": "firebase deploy --only firestore:rules", "deploy:indexes": "firebase deploy --only firestore:indexes", "deploy:firebase": "firebase deploy --only firestore", "firebase:emulator": "firebase emulators:start", "firebase:backup": "firebase firestore:export gs://widigital-d6110.appspot.com/backups/backup-$(date +%Y%m%d-%H%M%S)", "firebase:status": "firebase projects:list"}, "dependencies": {"@heroicons/react": "^2.0.18", "@tanstack/react-query": "^5.84.1", "@tanstack/react-query-devtools": "^5.84.1", "date-fns": "^2.30.0", "firebase": "^12.0.0", "lucide-react": "^0.292.0", "next": "14.0.0", "react": "^18", "react-dom": "^18", "recharts": "^2.8.0", "zustand": "^5.0.7"}, "devDependencies": {"@types/node": "^20.19.9", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.0", "postcss": "^8", "tailwindcss": "^3.3.0", "ts-node": "^10.9.2", "typescript": "^5"}}