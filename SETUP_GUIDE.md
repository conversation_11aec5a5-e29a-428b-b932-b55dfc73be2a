# 🦷 Dental Care Management System - Setup Guide

Panduan lengkap untuk menjalankan aplikasi Dental Care Management System dari awal.

## 📋 Prerequisites

Pastikan sistem Anda memiliki:

- **Node.js** (versi 18 atau lebih tinggi)
- **npm** (versi 8 atau lebih tinggi) 
- **Git** untuk version control
- **Firebase CLI** untuk deployment dan emulator
- **Browser modern** (Chrome, Firefox, Safari, Edge)

## 🚀 Langkah-langkah Setup

### 1. Clone Repository

```bash
git clone <repository-url>
cd dentalcare.id
```

### 2. Install Dependencies

```bash
# Install semua dependencies
npm install

# Verifikasi instalasi
npm list --depth=0
```

**Dependencies utama yang akan terinstall:**
- Next.js 14.0.0 (React Framework)
- Firebase 12.0.0 (Backend & Database)
- TanStack React Query (State Management)
- Zustand (Global State)
- Tailwind CSS (Styling)
- TypeScript (Type Safety)

### 3. Konfigurasi Environment

Aplikasi menggunakan konfigurasi Firebase yang sudah ada di `src/lib/firebase.ts`:

```typescript
const firebaseConfig = {
  apiKey: "AIzaSyDv9u4NhGvNFVVsou_IrXMO__rlfXfCKIk",
  authDomain: "widigital-d6110.firebaseapp.com",
  projectId: "widigital-d6110",
  storageBucket: "widigital-d6110.firebasestorage.app",
  messagingSenderId: "329879577024",
  appId: "1:329879577024:web:0d8752f8175569f67d6825"
};
```

### 4. Setup Firebase CLI

```bash
# Install Firebase CLI (jika belum ada)
npm install -g firebase-tools

# Login ke Firebase
firebase login

# Verifikasi project
firebase projects:list
```

### 5. Migrasi Database (Multi-Tenant)

```bash
# Jalankan migrasi untuk setup multi-tenant
npm run migrate

# Verifikasi status migrasi
npm run migrate:check
```

### 6. Build Aplikasi

```bash
# Build untuk production
npm run build
```

### 7. Jalankan Aplikasi Development

```bash
# Jalankan dalam mode development
npm run dev
```

Aplikasi akan berjalan di: **http://localhost:3000**

## 📁 Struktur Proyek

```
dentalcare.id/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── appointments/    # Halaman Appointments
│   │   ├── patients/        # Halaman Patients
│   │   ├── treatments/      # Halaman Treatments
│   │   ├── inventory/       # Halaman Inventory
│   │   ├── reports/         # Halaman Reports
│   │   └── settings/        # Halaman Settings
│   ├── components/          # React Components
│   ├── contexts/           # React Contexts
│   ├── hooks/              # Custom Hooks
│   ├── lib/                # Libraries & Config
│   ├── services/           # API Services
│   ├── types/              # TypeScript Types
│   └── utils/              # Utility Functions
├── public/                 # Static Assets
├── firebase.json           # Firebase Configuration
├── firestore.rules        # Firestore Security Rules
└── package.json           # Dependencies & Scripts
```

## 🛠️ Scripts yang Tersedia

```bash
# Development
npm run dev              # Jalankan development server
npm run build           # Build untuk production
npm run start           # Jalankan production server
npm run lint            # Linting code

# Database Migration
npm run migrate         # Jalankan migrasi multi-tenant
npm run migrate:check   # Cek status migrasi

# Firebase
npm run deploy:rules    # Deploy Firestore rules
npm run deploy:indexes  # Deploy Firestore indexes
npm run firebase:emulator # Jalankan Firebase emulator
```

## 🔧 Troubleshooting

### Error: Module not found
```bash
# Hapus node_modules dan reinstall
rm -rf node_modules package-lock.json
npm install
```

### Error: Firebase permission denied
```bash
# Login ulang ke Firebase
firebase logout
firebase login
```

### Error: Build failed
```bash
# Periksa TypeScript errors
npm run lint
# Fix errors dan build ulang
npm run build
```

## 🌐 Akses Aplikasi

Setelah setup berhasil:

1. **Development**: http://localhost:3000
2. **Production**: Sesuai deployment Firebase Hosting

## 📱 Fitur Utama

- ✅ **Multi-Tenant Architecture** - Isolasi data per klinik
- ✅ **Patient Management** - Kelola data pasien
- ✅ **Appointment Scheduling** - Jadwal appointment
- ✅ **Treatment Records** - Rekam medis treatment
- ✅ **Inventory Management** - Kelola stok obat/alat
- ✅ **Reports & Analytics** - Laporan dan analitik
- ✅ **Responsive Design** - Mobile-friendly
- ✅ **Real-time Updates** - Update real-time dengan Firebase

## 🔒 Security

- Firestore Security Rules untuk isolasi data
- Authentication dengan Firebase Auth
- Multi-tenant data isolation
- Role-based access control

## 🚀 Deployment ke Production

### Firebase Hosting

```bash
# Build aplikasi
npm run build

# Deploy ke Firebase Hosting
firebase deploy --only hosting

# Deploy Firestore rules
npm run deploy:rules

# Deploy Firestore indexes
npm run deploy:indexes
```

### Vercel Deployment

```bash
# Install Vercel CLI
npm install -g vercel

# Deploy ke Vercel
vercel --prod
```

## 🧪 Testing

### Manual Testing

1. **Buka aplikasi**: http://localhost:3000
2. **Test fitur utama**:
   - Login/Register
   - Tambah pasien baru
   - Buat appointment
   - Input treatment
   - Kelola inventory
   - Lihat reports

### Automated Testing

```bash
# Install testing dependencies (jika diperlukan)
npm install --save-dev jest @testing-library/react

# Jalankan tests
npm test
```

## 📊 Monitoring & Analytics

- **Firebase Console**: Monitor database usage
- **Vercel Analytics**: Track performance
- **Error Tracking**: Monitor aplikasi errors

## 📞 Support

Jika mengalami masalah:
1. Periksa console browser untuk error
2. Periksa terminal untuk error server
3. Pastikan Firebase project aktif
4. Verifikasi semua dependencies terinstall
5. Cek Firebase Console untuk quota limits
6. Pastikan internet connection stabil

## 🔄 Update & Maintenance

```bash
# Update dependencies
npm update

# Audit security vulnerabilities
npm audit
npm audit fix

# Update Firebase CLI
npm install -g firebase-tools@latest
```

---

**Status**: ✅ Aplikasi siap digunakan!
**Last Updated**: 2025-08-08
**Version**: 0.1.0
