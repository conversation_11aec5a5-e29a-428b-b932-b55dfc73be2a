import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { BillingService } from '@/services/billing';
import { TenantServiceRegistry } from '@/services/base/TenantServiceRegistry';
import { useTenant } from '@/contexts/TenantContext';
import { Invoice } from '@/types';

/**
 * Get all invoices
 */
export function useInvoices() {
  const { tenantId } = useTenant();

  return useQuery({
    queryKey: ['invoices', tenantId],
    queryFn: async () => {
      if (!tenantId) throw new Error('No tenant selected');
      const billingService = TenantServiceRegistry.getService(BillingService, tenantId, 'billing');
      return billingService.getInvoices();
    },
    enabled: !!tenantId,
    staleTime: 30000, // 30 seconds
  });
}

/**
 * Get invoice by ID
 */
export function useInvoice(id: string) {
  const { tenantId } = useTenant();

  return useQuery({
    queryKey: ['invoice', tenantId, id],
    queryFn: async () => {
      if (!tenantId) throw new Error('No tenant selected');
      const billingService = TenantServiceRegistry.getService(BillingService, tenantId, 'billing');
      return billingService.getInvoice(id);
    },
    enabled: !!tenantId && !!id,
  });
}

/**
 * Get invoices by patient
 */
export function useInvoicesByPatient(patientId: string) {
  const { tenantId } = useTenant();

  return useQuery({
    queryKey: ['invoices', tenantId, 'patient', patientId],
    queryFn: async () => {
      if (!tenantId) throw new Error('No tenant selected');
      const billingService = TenantServiceRegistry.getService(BillingService, tenantId, 'billing');
      return billingService.getInvoicesByPatient(patientId);
    },
    enabled: !!tenantId && !!patientId,
  });
}

/**
 * Get pending invoices
 */
export function usePendingInvoices() {
  const { tenantId } = useTenant();

  return useQuery({
    queryKey: ['invoices', tenantId, 'pending'],
    queryFn: async () => {
      if (!tenantId) throw new Error('No tenant selected');
      const billingService = TenantServiceRegistry.getService(BillingService, tenantId, 'billing');
      return billingService.getPendingInvoices();
    },
    enabled: !!tenantId,
    staleTime: 30000, // 30 seconds
  });
}

/**
 * Get revenue statistics
 */
export function useRevenueStats(startDate: string, endDate: string) {
  const { tenantId } = useTenant();

  return useQuery({
    queryKey: ['revenue-stats', tenantId, startDate, endDate],
    queryFn: async () => {
      if (!tenantId) throw new Error('No tenant selected');
      const billingService = TenantServiceRegistry.getService(BillingService, tenantId, 'billing');
      return billingService.getRevenueStats(startDate, endDate);
    },
    enabled: !!tenantId && !!startDate && !!endDate,
    staleTime: 60000, // 1 minute
  });
}

/**
 * Create new invoice
 */
export function useCreateInvoice() {
  const queryClient = useQueryClient();
  const { tenantId } = useTenant();

  return useMutation({
    mutationFn: (invoiceData: Omit<Invoice, 'id'>) => {
      if (!tenantId) throw new Error('No tenant selected');
      const billingService = TenantServiceRegistry.getService(BillingService, tenantId, 'billing');
      return billingService.createInvoice(invoiceData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['invoices', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['revenue-stats', tenantId] });
    },
    onError: (error) => {
      console.error('Error creating invoice:', error);
    }
  });
}

/**
 * Create invoice from appointment
 */
export function useCreateInvoiceFromAppointment() {
  const queryClient = useQueryClient();
  const { tenantId } = useTenant();

  return useMutation({
    mutationFn: ({
      appointmentId,
      patientId,
      patientName,
      treatments
    }: {
      appointmentId: string;
      patientId: string;
      patientName: string;
      treatments: { name: string; price: number; quantity?: number }[];
    }) => {
      if (!tenantId) throw new Error('No tenant selected');
      const billingService = TenantServiceRegistry.getService(BillingService, tenantId, 'billing');
      return billingService.createInvoiceFromAppointment(appointmentId, patientId, patientName, treatments);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['invoices', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['revenue-stats', tenantId] });
    },
    onError: (error) => {
      console.error('Error creating invoice from appointment:', error);
    }
  });
}

/**
 * Update invoice
 */
export function useUpdateInvoice() {
  const queryClient = useQueryClient();
  const { tenantId } = useTenant();

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<Invoice> }) => {
      if (!tenantId) throw new Error('No tenant selected');
      const billingService = TenantServiceRegistry.getService(BillingService, tenantId, 'billing');
      return billingService.updateInvoice(id, updates);
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['invoices', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['invoice', tenantId, id] });
      queryClient.invalidateQueries({ queryKey: ['revenue-stats', tenantId] });
    },
    onError: (error) => {
      console.error('Error updating invoice:', error);
    }
  });
}

/**
 * Mark invoice as paid
 */
export function useMarkInvoiceAsPaid() {
  const queryClient = useQueryClient();
  const { tenantId } = useTenant();

  return useMutation({
    mutationFn: ({ id, paymentMethod }: { id: string; paymentMethod: string }) => {
      if (!tenantId) throw new Error('No tenant selected');
      const billingService = TenantServiceRegistry.getService(BillingService, tenantId, 'billing');
      return billingService.markAsPaid(id, paymentMethod);
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['invoices', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['invoice', tenantId, id] });
      queryClient.invalidateQueries({ queryKey: ['revenue-stats', tenantId] });
    },
    onError: (error) => {
      console.error('Error marking invoice as paid:', error);
    }
  });
}

/**
 * Delete invoice
 */
export function useDeleteInvoice() {
  const queryClient = useQueryClient();
  const { tenantId } = useTenant();

  return useMutation({
    mutationFn: (id: string) => {
      if (!tenantId) throw new Error('No tenant selected');
      const billingService = TenantServiceRegistry.getService(BillingService, tenantId, 'billing');
      return billingService.deleteInvoice(id);
    },
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: ['invoices', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['invoice', tenantId, id] });
      queryClient.invalidateQueries({ queryKey: ['revenue-stats', tenantId] });
    },
    onError: (error) => {
      console.error('Error deleting invoice:', error);
    }
  });
}
