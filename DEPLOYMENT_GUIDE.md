# 🚀 Multi-Tenant DentalCare Deployment Guide

## Pre-Deployment Checklist

### ✅ **1. Backup Data**
```bash
# Export existing Firestore data
firebase firestore:export gs://your-project-backup/backup-$(date +%Y%m%d)
```

### ✅ **2. Environment Setup**
Pastikan environment variables sudah benar:
```env
NEXT_PUBLIC_FIREBASE_API_KEY=your-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
NEXT_PUBLIC_FIREBASE_APP_ID=your-app-id
```

## Deployment Steps

### 🔐 **Step 1: Deploy Security Rules**
```bash
# Deploy Firestore security rules
npm run deploy:rules

# Verify rules deployment
firebase firestore:rules:get
```

### 📊 **Step 2: Run Data Migration**
```bash
# Check current data structure
npm run migrate:check

# Run migration (BACKUP FIRST!)
npm run migrate
```

### 🏗️ **Step 3: Deploy Application**
```bash
# Build application
npm run build

# Deploy to your hosting platform
# For Vercel:
vercel --prod

# For Firebase Hosting:
firebase deploy --only hosting
```

### 🧪 **Step 4: Post-Deployment Testing**

#### Test Multi-Tenant Isolation
1. **Create Test Tenants**:
   ```bash
   # Use the migration script to setup tenants
   npm run migrate

   # Or manually create tenants through the application UI
   # by registering different clinic accounts
   ```

2. **Verify Data Isolation**:
   - Login with tenant 1 user
   - Create some patients/appointments
   - Switch to tenant 2
   - Verify tenant 1 data is not visible
   - Create different data in tenant 2
   - Switch back to tenant 1
   - Verify tenant 2 data is not visible

3. **Test Security Rules**:
   ```javascript
   // This should fail (cross-tenant access)
   try {
     await getDoc(doc(db, 'dentalcare', 'other-tenant-id', 'patients', 'patient-id'));
   } catch (error) {
     console.log('✅ Security rules working:', error.code); // Should be 'permission-denied'
   }
   ```

## Monitoring & Maintenance

### 📈 **Performance Monitoring**
```bash
# Monitor Firestore usage
firebase firestore:usage

# Check security rule evaluations
# Go to Firebase Console > Firestore > Rules > Metrics
```

### 🔍 **Health Checks**
Create a health check endpoint:
```typescript
// pages/api/health.ts
export default async function handler(req, res) {
  try {
    // Test database connection
    await getDoc(doc(db, 'health', 'check'));
    
    res.status(200).json({ 
      status: 'healthy',
      timestamp: new Date().toISOString(),
      multiTenant: true
    });
  } catch (error) {
    res.status(500).json({ 
      status: 'unhealthy', 
      error: error.message 
    });
  }
}
```

### 🚨 **Error Monitoring**
Set up error tracking:
```typescript
// utils/errorTracking.ts
export function trackError(error: Error, context: any) {
  console.error('Application Error:', {
    message: error.message,
    stack: error.stack,
    context,
    timestamp: new Date().toISOString()
  });
  
  // Send to your error tracking service
  // e.g., Sentry, LogRocket, etc.
}
```

## Rollback Plan

### 🔄 **If Migration Fails**
1. **Stop Application**:
   ```bash
   # Stop current deployment
   vercel --prod --env MAINTENANCE_MODE=true
   ```

2. **Restore Backup**:
   ```bash
   # Restore from backup
   firebase firestore:import gs://your-project-backup/backup-YYYYMMDD
   ```

3. **Revert Code**:
   ```bash
   # Revert to previous version
   git revert HEAD
   vercel --prod
   ```

### 🔄 **If Security Rules Fail**
```bash
# Revert to previous rules
firebase firestore:rules:release --release-id=previous-release-id
```

## Performance Optimization

### 🚀 **Database Indexes**
Create composite indexes for tenant queries:
```javascript
// Required indexes (create in Firebase Console):
// Collection: dentalcare/{tenantId}/patients
// Fields: status (Ascending), name (Ascending)

// Collection: dentalcare/{tenantId}/appointments  
// Fields: date (Ascending), time (Ascending)
// Fields: patientId (Ascending), date (Descending)
// Fields: doctorId (Ascending), date (Descending)
```

### 🎯 **Caching Strategy**
```typescript
// Implement service worker caching
// Cache tenant-specific data with proper keys
const cacheKey = `tenant-${tenantId}-patients`;
```

## Security Checklist

### 🔒 **Post-Deployment Security Verification**
- [ ] Security rules prevent cross-tenant access
- [ ] User authentication works correctly
- [ ] Tenant switching requires proper permissions
- [ ] API endpoints validate tenant access
- [ ] No sensitive data in client-side code
- [ ] HTTPS enforced on all endpoints

### 🛡️ **Ongoing Security**
```bash
# Regular security audits
npm audit
npm audit fix

# Update dependencies
npm update

# Monitor Firebase security
# Check Firebase Console > Authentication > Settings > Authorized domains
```

## Troubleshooting

### ❌ **Common Issues**

1. **"No tenant selected" errors**:
   ```typescript
   // Check TenantProvider is wrapping the app
   // Verify user has tenantId in profile
   ```

2. **Permission denied errors**:
   ```bash
   # Check security rules
   firebase firestore:rules:get
   
   # Verify user authentication
   # Check user's tenantId matches requested tenant
   ```

3. **Data not appearing**:
   ```typescript
   // Verify correct collection path
   console.log('Collection path:', `dentalcare/${tenantId}/patients`);
   
   // Check tenant context
   console.log('Current tenant:', tenantId);
   ```

4. **Migration stuck**:
   ```bash
   # Check Firestore quotas
   # Reduce batch size in migration script
   # Run migration in smaller chunks
   ```

### 📞 **Support Contacts**
- Technical Issues: [your-tech-email]
- Security Concerns: [your-security-email]
- Business Questions: [your-business-email]

## Success Metrics

### 📊 **Key Performance Indicators**
- [ ] Zero cross-tenant data leakage incidents
- [ ] < 2s average page load time
- [ ] > 99.9% uptime
- [ ] < 100ms database query response time
- [ ] Zero security rule violations

### 📈 **Monitoring Dashboard**
Set up monitoring for:
- Active tenants count
- Database operations per tenant
- Error rates by tenant
- User session duration
- Feature usage by tenant

---

## 🎉 **Deployment Complete!**

Your multi-tenant DentalCare application is now live! 

**Next Steps:**
1. Monitor the application for 24-48 hours
2. Gather user feedback
3. Plan feature rollouts per tenant
4. Set up automated backups
5. Create tenant onboarding process

**Remember:** Always test changes in a staging environment first!
