import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { UserService } from '@/services/users';
import { TenantServiceRegistry } from '@/services/base/TenantServiceRegistry';
import { useTenant } from '@/contexts/TenantContext';
import { User } from '@/types';

/**
 * Get all users for current tenant
 */
export function useUsers() {
  const { tenantId } = useTenant();

  return useQuery({
    queryKey: ['users', tenantId],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const userService = TenantServiceRegistry.getService(UserService, tenantId, 'users');
      return userService.getUsers();
    },
    enabled: !!tenantId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Get user by ID
 */
export function useUser(id: string) {
  const { tenantId } = useTenant();

  return useQuery({
    queryKey: ['user', tenantId, id],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const userService = TenantServiceRegistry.getService(UserService, tenantId, 'users');
      return userService.getUser(id);
    },
    enabled: !!id && !!tenantId,
  });
}

/**
 * Search users
 */
export function useSearchUsers(searchTerm: string) {
  const { tenantId } = useTenant();

  return useQuery({
    queryKey: ['users', 'search', tenantId, searchTerm],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const userService = TenantServiceRegistry.getService(UserService, tenantId, 'users');
      return userService.searchUsers(searchTerm);
    },
    enabled: !!tenantId && searchTerm.length >= 2,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

/**
 * Get users by role
 */
export function useUsersByRole(role: User['role']) {
  const { tenantId } = useTenant();

  return useQuery({
    queryKey: ['users', 'role', tenantId, role],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const userService = TenantServiceRegistry.getService(UserService, tenantId, 'users');
      return userService.getUsersByRole(role);
    },
    enabled: !!tenantId && !!role,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Get user statistics
 */
export function useUserStats() {
  const { tenantId } = useTenant();

  return useQuery({
    queryKey: ['users', 'stats', tenantId],
    queryFn: () => {
      if (!tenantId) throw new Error('No tenant selected');
      const userService = TenantServiceRegistry.getService(UserService, tenantId, 'users');
      return userService.getUserStats();
    },
    enabled: !!tenantId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Create new user
 */
export function useCreateUser() {
  const queryClient = useQueryClient();
  const { tenantId } = useTenant();

  return useMutation({
    mutationFn: (userData: Omit<User, 'id'>) => {
      if (!tenantId) throw new Error('No tenant selected');
      const userService = TenantServiceRegistry.getService(UserService, tenantId, 'users');
      return userService.createUser(userData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['users', 'stats', tenantId] });
    },
    onError: (error) => {
      console.error('Error creating user:', error);
    }
  });
}

/**
 * Update user
 */
export function useUpdateUser() {
  const queryClient = useQueryClient();
  const { tenantId } = useTenant();

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<User> }) => {
      if (!tenantId) throw new Error('No tenant selected');
      const userService = TenantServiceRegistry.getService(UserService, tenantId, 'users');
      return userService.updateUser(id, updates);
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['users', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['user', tenantId, id] });
      queryClient.invalidateQueries({ queryKey: ['users', 'stats', tenantId] });
    },
    onError: (error) => {
      console.error('Error updating user:', error);
    }
  });
}

/**
 * Delete user
 */
export function useDeleteUser() {
  const queryClient = useQueryClient();
  const { tenantId } = useTenant();

  return useMutation({
    mutationFn: (id: string) => {
      if (!tenantId) throw new Error('No tenant selected');
      const userService = TenantServiceRegistry.getService(UserService, tenantId, 'users');
      return userService.deleteUser(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users', tenantId] });
      queryClient.invalidateQueries({ queryKey: ['users', 'stats', tenantId] });
    },
    onError: (error) => {
      console.error('Error deleting user:', error);
    }
  });
}

/**
 * Real-time users subscription
 */
export function useRealTimeUsers() {
  const { tenantId } = useTenant();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    if (!tenantId) {
      setUsers([]);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    const userService = TenantServiceRegistry.getService(UserService, tenantId, 'users');
    const unsubscribe = userService.subscribeToUsers(
      (updatedUsers: User[]) => {
        setUsers(updatedUsers);
        setLoading(false);
      }
    );

    return () => {
      unsubscribe();
    };
  }, [tenantId]);

  return { users, loading, error };
}

/**
 * Get role utilities
 */
export function useRoleUtils() {
  const { tenantId } = useTenant();

  const getRoleDisplayName = (role: User['role']): string => {
    if (!tenantId) return role;
    const userService = TenantServiceRegistry.getService(UserService, tenantId, 'users');
    return userService.getRoleDisplayName(role);
  };

  const getRolePermissions = (role: User['role']): string[] => {
    if (!tenantId) return [];
    const userService = TenantServiceRegistry.getService(UserService, tenantId, 'users');
    return userService.getRolePermissions(role);
  };

  const hasPermission = (userRole: User['role'], requiredRole: User['role']): boolean => {
    if (!tenantId) return false;
    const userService = TenantServiceRegistry.getService(UserService, tenantId, 'users');
    return userService.hasPermission(userRole, requiredRole);
  };

  const generateDefaultPassword = (): string => {
    if (!tenantId) return '';
    const userService = TenantServiceRegistry.getService(UserService, tenantId, 'users');
    return userService.generateDefaultPassword();
  };

  return {
    getRoleDisplayName,
    getRolePermissions,
    hasPermission,
    generateDefaultPassword
  };
}
