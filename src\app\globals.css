@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }
  
  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }

  .input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }
  
  .sidebar-link {
    @apply flex items-center px-4 py-3 text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-lg transition-colors duration-200;
  }
  
  .sidebar-link.active {
    @apply bg-primary-100 text-primary-700 font-medium;
  }
  
  .status-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .status-scheduled {
    @apply bg-blue-100 text-blue-800;
  }
  
  .status-confirmed {
    @apply bg-green-100 text-green-800;
  }
  
  .status-in-progress {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .status-completed {
    @apply bg-gray-100 text-gray-800;
  }
  
  .status-cancelled {
    @apply bg-red-100 text-red-800;
  }
  
  .status-active {
    @apply bg-green-100 text-green-800;
  }
  
  .status-inactive {
    @apply bg-gray-100 text-gray-800;
  }
  
  .status-paid {
    @apply bg-green-100 text-green-800;
  }
  
  .status-pending {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .status-overdue {
    @apply bg-red-100 text-red-800;
  }
  
  .status-in-stock {
    @apply bg-green-100 text-green-800;
  }
  
  .status-low-stock {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .status-out-of-stock {
    @apply bg-red-100 text-red-800;
  }
}
