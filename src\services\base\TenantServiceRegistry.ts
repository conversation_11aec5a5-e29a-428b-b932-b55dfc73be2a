import { TenantService } from './TenantService';

/**
 * Registry for managing tenant-aware service instances
 * Provides singleton pattern for service instances per tenant
 */
export class TenantServiceRegistry {
  private static instances = new Map<string, Map<string, TenantService>>();

  /**
   * Get or create a service instance for a specific tenant
   */
  static getService<T extends TenantService>(
    ServiceClass: new (tenantId: string) => T,
    tenantId: string,
    serviceKey: string
  ): T {
    if (!tenantId) {
      throw new Error('TenantServiceRegistry requires a valid tenantId');
    }

    // Get or create tenant map
    if (!this.instances.has(tenantId)) {
      this.instances.set(tenantId, new Map());
    }

    const tenantServices = this.instances.get(tenantId)!;

    // Get or create service instance
    if (!tenantServices.has(serviceKey)) {
      const serviceInstance = new ServiceClass(tenantId);
      tenantServices.set(serviceKey, serviceInstance);
    }

    return tenantServices.get(serviceKey) as T;
  }

  /**
   * Clear all service instances for a tenant
   */
  static clearTenant(tenantId: string): void {
    this.instances.delete(tenantId);
  }

  /**
   * Clear all service instances
   */
  static clearAll(): void {
    this.instances.clear();
  }

  /**
   * Get all registered tenants
   */
  static getRegisteredTenants(): string[] {
    return Array.from(this.instances.keys());
  }

  /**
   * Check if a tenant has any registered services
   */
  static hasTenant(tenantId: string): boolean {
    return this.instances.has(tenantId);
  }

  /**
   * Get service count for a tenant
   */
  static getServiceCount(tenantId: string): number {
    const tenantServices = this.instances.get(tenantId);
    return tenantServices ? tenantServices.size : 0;
  }
}
