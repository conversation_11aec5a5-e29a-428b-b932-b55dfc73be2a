'use client';

import { useState } from 'react';
import Header from '@/components/Layout/Header';
import UserForm from '@/components/Users/<USER>';
import { useUsers, useDeleteUser, useRoleUtils } from '@/hooks/useUsers';
import { User } from '@/types';
import { 
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  UserGroupIcon,
  EyeIcon
} from '@heroicons/react/24/outline';

export default function UsersPage() {
  const [showUserForm, setShowUserForm] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<User['role'] | 'all'>('all');

  const { data: users = [], isLoading } = useUsers();
  const deleteUserMutation = useDeleteUser();
  const { getRoleDisplayName } = useRoleUtils();

  const roles: User['role'][] = ['admin', 'manager', 'doctor', 'receptionist', 'assistant'];

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = roleFilter === 'all' || user.role === roleFilter;
    return matchesSearch && matchesRole;
  });

  const handleUserSuccess = () => {
    setShowUserForm(false);
    setEditingUser(null);
  };

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setShowUserForm(true);
  };

  const handleDeleteUser = async (userId: string) => {
    if (window.confirm('Apakah Anda yakin ingin menghapus user ini?')) {
      try {
        await deleteUserMutation.mutateAsync(userId);
      } catch (error) {
        console.error('Error deleting user:', error);
      }
    }
  };

  const getRoleColor = (role: User['role']) => {
    const colors: Record<User['role'], string> = {
      'admin': 'bg-red-100 text-red-800',
      'manager': 'bg-purple-100 text-purple-800',
      'doctor': 'bg-blue-100 text-blue-800',
      'receptionist': 'bg-green-100 text-green-800',
      'assistant': 'bg-yellow-100 text-yellow-800'
    };
    return colors[role] || 'bg-gray-100 text-gray-800';
  };

  if (showUserForm) {
    return (
      <div className="flex-1 overflow-auto">
        <Header 
          title={editingUser ? 'Edit User' : 'Tambah User Baru'} 
          subtitle={editingUser ? 'Update informasi user' : 'Tambahkan staff baru ke sistem'}
        />
        
        <main className="p-6">
          <UserForm 
            user={editingUser || undefined}
            onSuccess={handleUserSuccess}
            onCancel={() => {
              setShowUserForm(false);
              setEditingUser(null);
            }}
          />
        </main>
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-auto">
      <Header 
        title="User Management" 
        subtitle="Kelola user dan staff klinik"
        action={
          <button
            onClick={() => setShowUserForm(true)}
            className="btn-primary flex items-center space-x-2"
          >
            <PlusIcon className="w-5 h-5" />
            <span>Tambah User</span>
          </button>
        }
      />
      
      <main className="p-6">
        <div className="space-y-6">
          {/* User Stats */}
          <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
            <div className="card text-center">
              <UserGroupIcon className="w-8 h-8 text-blue-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-gray-900">{users.length}</p>
              <p className="text-sm text-gray-600">Total User</p>
            </div>
            {roles.map(role => {
              const count = users.filter(u => u.role === role).length;
              return (
                <div key={role} className="card text-center">
                  <div className={`w-8 h-8 mx-auto mb-2 rounded-full flex items-center justify-center ${getRoleColor(role)}`}>
                    <span className="text-xs font-bold">{count}</span>
                  </div>
                  <p className="text-sm font-medium text-gray-900">{getRoleDisplayName(role)}</p>
                </div>
              );
            })}
          </div>

          {/* Filters */}
          <div className="card">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Cari user berdasarkan nama atau email..."
                  className="input pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <select
                className="input w-full sm:w-48"
                value={roleFilter}
                onChange={(e) => setRoleFilter(e.target.value as User['role'] | 'all')}
              >
                <option value="all">Semua Role</option>
                {roles.map(role => (
                  <option key={role} value={role}>
                    {getRoleDisplayName(role)}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* User List */}
          <div className="card">
            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                <span className="ml-2 text-gray-600">Memuat data user...</span>
              </div>
            ) : filteredUsers.length === 0 ? (
              <div className="text-center py-12">
                <UserGroupIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {searchTerm || roleFilter !== 'all' ? 'Tidak ada user ditemukan' : 'Belum ada user'}
                </h3>
                <p className="text-gray-600 mb-4">
                  {searchTerm || roleFilter !== 'all' 
                    ? 'Coba ubah filter pencarian' 
                    : 'Mulai dengan menambahkan user pertama'
                  }
                </p>
                {(!searchTerm && roleFilter === 'all') && (
                  <button
                    onClick={() => setShowUserForm(true)}
                    className="btn-primary"
                  >
                    Tambah User Pertama
                  </button>
                )}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        User
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Role
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Email
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Dibuat
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Aksi
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredUsers.map((user) => (
                      <tr key={user.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                              {user.avatar ? (
                                <img 
                                  src={user.avatar} 
                                  alt={user.name}
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <UserGroupIcon className="w-6 h-6 text-gray-400" />
                              )}
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">{user.name}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`status-badge ${getRoleColor(user.role)}`}>
                            {getRoleDisplayName(user.role)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{user.email}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {user.createdAt ? new Date(user.createdAt).toLocaleDateString('id-ID') : '-'}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => setSelectedUser(user)}
                              className="text-primary-600 hover:text-primary-900"
                              title="Lihat Detail"
                            >
                              <EyeIcon className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleEditUser(user)}
                              className="text-blue-600 hover:text-blue-900"
                              title="Edit"
                            >
                              <PencilIcon className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleDeleteUser(user.id)}
                              className="text-red-600 hover:text-red-900"
                              title="Hapus"
                              disabled={deleteUserMutation.isPending}
                            >
                              <TrashIcon className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>

        {/* User Detail Modal */}
        {selectedUser && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-md w-full p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-gray-900">Detail User</h3>
                <button 
                  onClick={() => setSelectedUser(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>
              
              <div className="space-y-4">
                <div className="text-center">
                  <div className="w-20 h-20 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden mx-auto mb-4">
                    {selectedUser.avatar ? (
                      <img 
                        src={selectedUser.avatar} 
                        alt={selectedUser.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <UserGroupIcon className="w-10 h-10 text-gray-400" />
                    )}
                  </div>
                  <h4 className="text-lg font-medium text-gray-900">{selectedUser.name}</h4>
                  <span className={`status-badge ${getRoleColor(selectedUser.role)} mt-2`}>
                    {getRoleDisplayName(selectedUser.role)}
                  </span>
                </div>

                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Email</label>
                    <p className="text-gray-900">{selectedUser.email}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Bergabung</label>
                    <p className="text-gray-900">
                      {selectedUser.createdAt ? new Date(selectedUser.createdAt).toLocaleDateString('id-ID') : '-'}
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-end space-x-3 mt-6">
                <button 
                  onClick={() => {
                    setSelectedUser(null);
                    handleEditUser(selectedUser);
                  }}
                  className="btn-primary"
                >
                  Edit User
                </button>
                <button 
                  onClick={() => setSelectedUser(null)}
                  className="btn-secondary"
                >
                  Tutup
                </button>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
