# 🚀 Quick Start - Dental Care Management System

## Langkah Cepat Menjalankan Aplikasi

### 1. Install Dependencies
```bash
npm install
```

### 2. Jalankan Migrasi Database
```bash
npm run migrate
```

### 3. Jalankan Aplikasi
```bash
npm run dev
```

### 4. <PERSON><PERSON>es Aplikasi
Buka browser: **http://localhost:3000**

---

## ✅ Verifikasi Setup Berhasil

Jika setup berhasil, Anda akan melihat:
- ✅ Terminal menampilkan: `✓ Ready in 1895ms`
- ✅ Browser dapat mengakses http://localhost:3000 (atau port lain jika 3000 sedang digunakan)
- ✅ Dashboard aplikasi muncul dengan menu navigasi
- ✅ Tidak ada error di console browser
- ✅ Project sudah dibersihkan dari file yang tidak diperlukan

## 🛠️ Commands Penting

```bash
# Development
npm run dev              # Jalankan development server
npm run build           # Build untuk production

# Database
npm run migrate         # Setup multi-tenant database
npm run migrate:check   # Cek status migrasi

# Firebase
firebase projects:list  # Lihat project Firebase
```

## 📱 Fitur yang Tersedia

- **Dashboard** - Overview klinik
- **Patients** - Manajemen pasien
- **Appointments** - Jadwal appointment
- **Treatments** - Rekam medis treatment
- **Inventory** - Kelola stok
- **Reports** - Laporan dan analitik
- **Settings** - Pengaturan klinik

## 🔧 Troubleshooting Cepat

**Error: Module not found**
```bash
rm -rf node_modules package-lock.json
npm install
```

**Error: Permission denied**
```bash
# Stop development server (Ctrl+C)
npm run dev
```

**Error: Firebase**
```bash
firebase login
firebase projects:list
```

---

**Status**: ✅ Aplikasi siap digunakan!
**URL**: http://localhost:3000
