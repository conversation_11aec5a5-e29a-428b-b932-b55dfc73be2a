/**
 * Migration Script Runner (JavaScript version)
 * 
 * This script helps you run the multi-tenant migration safely.
 * Run this script once to migrate your existing single-tenant data
 * to the new multi-tenant structure.
 * 
 * Usage:
 * 1. Make sure you have a backup of your Firestore data
 * 2. Update Firebase configuration if needed
 * 3. Run: node src/scripts/runMigration.js migrate
 * 
 * WARNING: This is a one-time operation that modifies your database structure.
 * Make sure to test in a development environment first!
 */

const { initializeApp } = require('firebase/app');
const { 
  getFirestore,
  collection, 
  doc, 
  getDocs, 
  getDoc,
  setDoc, 
  writeBatch,
  query,
  where
} = require('firebase/firestore');

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDv9u4NhGvNFVVsou_IrXMO__rlfXfCKIk",
  authDomain: "widigital-d6110.firebaseapp.com",
  projectId: "widigital-d6110",
  storageBucket: "widigital-d6110.firebasestorage.app",
  messagingSenderId: "329879577024",
  appId: "1:329879577024:web:0d8752f8175569f67d6825"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

/**
 * Migration utility to convert single-tenant structure to multi-tenant
 */
class DataMigration {
  
  /**
   * Migrate all existing data to the new multi-tenant structure
   */
  static async migrateToMultiTenant() {
    console.log('Starting multi-tenant migration...');
    
    try {
      // Step 1: Migrate users and create tenant mappings
      const tenantMappings = await this.migrateUsers();
      
      // Step 2: Migrate patients
      await this.migratePatients(tenantMappings);
      
      // Step 3: Migrate appointments
      await this.migrateAppointments(tenantMappings);
      
      // Step 4: Migrate treatments
      await this.migrateTreatments(tenantMappings);
      
      // Step 5: Migrate inventory
      await this.migrateInventory(tenantMappings);
      
      // Step 6: Migrate clinic settings
      await this.migrateClinicSettings(tenantMappings);
      
      console.log('Multi-tenant migration completed successfully!');
    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  }

  /**
   * Migrate users and create tenant mappings
   */
  static async migrateUsers() {
    console.log('Migrating users...');
    const tenantMappings = new Map(); // clinicId -> tenantId
    
    const usersSnapshot = await getDocs(collection(db, 'users'));
    const batch = writeBatch(db);
    
    for (const userDoc of usersSnapshot.docs) {
      const userData = userDoc.data();
      const oldClinicId = userData.clinicId || userData.tenantId;
      
      // Generate or get tenant ID for this clinic
      let tenantId = tenantMappings.get(oldClinicId);
      if (!tenantId) {
        tenantId = `tenant_${oldClinicId}_${Date.now()}`;
        tenantMappings.set(oldClinicId, tenantId);
      }
      
      // Update user document with tenantId
      const updatedUserData = {
        ...userData,
        tenantId,
        updatedAt: new Date().toISOString()
      };
      delete updatedUserData.clinicId; // Remove old clinicId
      
      // Update global user document
      batch.set(doc(db, 'users', userDoc.id), updatedUserData, { merge: true });
      
      // Create tenant-specific user document
      batch.set(
        doc(db, 'dentalcare', tenantId, 'users', userDoc.id),
        updatedUserData
      );
    }
    
    await batch.commit();
    console.log(`Migrated ${usersSnapshot.docs.length} users`);
    return tenantMappings;
  }

  /**
   * Migrate patients to tenant-specific collections
   */
  static async migratePatients(tenantMappings) {
    console.log('Migrating patients...');
    
    const patientsSnapshot = await getDocs(collection(db, 'patients'));
    const batch = writeBatch(db);
    let batchCount = 0;
    
    for (const patientDoc of patientsSnapshot.docs) {
      const patientData = patientDoc.data();
      const oldClinicId = patientData.clinicId;
      const tenantId = tenantMappings.get(oldClinicId);
      
      if (!tenantId) {
        console.warn(`No tenant mapping found for clinicId: ${oldClinicId}`);
        continue;
      }
      
      // Remove clinicId and update timestamps
      const updatedPatientData = {
        ...patientData,
        updatedAt: new Date().toISOString()
      };
      delete updatedPatientData.clinicId;
      
      // Create patient in tenant-specific collection
      batch.set(
        doc(db, 'dentalcare', tenantId, 'patients', patientDoc.id),
        updatedPatientData
      );
      
      batchCount++;
      
      // Commit batch every 500 operations (Firestore limit)
      if (batchCount >= 500) {
        await batch.commit();
        console.log(`Committed batch of ${batchCount} patients`);
        batchCount = 0;
      }
    }
    
    if (batchCount > 0) {
      await batch.commit();
    }
    
    console.log(`Migrated ${patientsSnapshot.docs.length} patients`);
  }

  /**
   * Migrate appointments to tenant-specific collections
   */
  static async migrateAppointments(tenantMappings) {
    console.log('Migrating appointments...');
    
    try {
      const appointmentsSnapshot = await getDocs(collection(db, 'appointments'));
      const batch = writeBatch(db);
      let batchCount = 0;
      
      for (const appointmentDoc of appointmentsSnapshot.docs) {
        const appointmentData = appointmentDoc.data();
        const oldClinicId = appointmentData.clinicId;
        const tenantId = tenantMappings.get(oldClinicId);
        
        if (!tenantId) {
          console.warn(`No tenant mapping found for clinicId: ${oldClinicId}`);
          continue;
        }
        
        // Remove clinicId and update timestamps
        const updatedAppointmentData = {
          ...appointmentData,
          updatedAt: new Date().toISOString()
        };
        delete updatedAppointmentData.clinicId;
        
        // Create appointment in tenant-specific collection
        batch.set(
          doc(db, 'dentalcare', tenantId, 'appointments', appointmentDoc.id),
          updatedAppointmentData
        );
        
        batchCount++;
        
        if (batchCount >= 500) {
          await batch.commit();
          console.log(`Committed batch of ${batchCount} appointments`);
          batchCount = 0;
        }
      }
      
      if (batchCount > 0) {
        await batch.commit();
      }
      
      console.log(`Migrated ${appointmentsSnapshot.docs.length} appointments`);
    } catch (error) {
      console.log('No appointments collection found, skipping...');
    }
  }

  /**
   * Migrate treatments to tenant-specific collections
   */
  static async migrateTreatments(tenantMappings) {
    console.log('Migrating treatments...');
    
    try {
      const treatmentsSnapshot = await getDocs(collection(db, 'treatments'));
      
      // Treatments might be shared across tenants, so we'll copy them to each tenant
      for (const [clinicId, tenantId] of tenantMappings) {
        const batch = writeBatch(db);
        
        for (const treatmentDoc of treatmentsSnapshot.docs) {
          const treatmentData = treatmentDoc.data();
          
          batch.set(
            doc(db, 'dentalcare', tenantId, 'treatments', treatmentDoc.id),
            {
              ...treatmentData,
              updatedAt: new Date().toISOString()
            }
          );
        }
        
        await batch.commit();
      }
      
      console.log(`Migrated treatments to ${tenantMappings.size} tenants`);
    } catch (error) {
      console.log('No treatments collection found, skipping...');
    }
  }

  /**
   * Migrate inventory to tenant-specific collections
   */
  static async migrateInventory(tenantMappings) {
    console.log('Migrating inventory...');
    
    try {
      const inventorySnapshot = await getDocs(collection(db, 'inventory'));
      
      for (const [clinicId, tenantId] of tenantMappings) {
        const batch = writeBatch(db);
        
        for (const inventoryDoc of inventorySnapshot.docs) {
          const inventoryData = inventoryDoc.data();
          
          batch.set(
            doc(db, 'dentalcare', tenantId, 'inventory', inventoryDoc.id),
            {
              ...inventoryData,
              updatedAt: new Date().toISOString()
            }
          );
        }
        
        await batch.commit();
      }
      
      console.log(`Migrated inventory to ${tenantMappings.size} tenants`);
    } catch (error) {
      console.log('No inventory collection found, skipping...');
    }
  }

  /**
   * Migrate clinic settings
   */
  static async migrateClinicSettings(tenantMappings) {
    console.log('Migrating clinic settings...');
    
    try {
      const clinicsSnapshot = await getDocs(collection(db, 'clinics'));
      
      for (const clinicDoc of clinicsSnapshot.docs) {
        const clinicData = clinicDoc.data();
        const tenantId = tenantMappings.get(clinicDoc.id);
        
        if (!tenantId) {
          console.warn(`No tenant mapping found for clinic: ${clinicDoc.id}`);
          continue;
        }
        
        // Create tenant settings
        await setDoc(
          doc(db, 'dentalcare', tenantId, 'settings', 'clinic'),
          {
            id: tenantId,
            ...clinicData,
            updatedAt: new Date().toISOString()
          }
        );
      }
      
      console.log(`Migrated ${clinicsSnapshot.docs.length} clinic settings`);
    } catch (error) {
      console.log('No clinics collection found, creating default settings...');
      
      // Create default settings for each tenant
      for (const [clinicId, tenantId] of tenantMappings) {
        await setDoc(
          doc(db, 'dentalcare', tenantId, 'settings', 'clinic'),
          {
            id: tenantId,
            name: `Klinik ${clinicId}`,
            address: '',
            phone: '',
            email: '',
            settings: {
              timezone: 'Asia/Jakarta',
              currency: 'IDR',
              dateFormat: 'DD/MM/YYYY',
              businessHours: {
                start: '08:00',
                end: '17:00',
                days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
              }
            },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        );
      }
    }
  }
}

async function runMigration() {
  console.log('🚀 Starting Multi-Tenant Migration');
  console.log('=====================================');
  
  // Safety check
  const environment = process.env.NODE_ENV || 'development';
  console.log(`Environment: ${environment}`);
  
  if (environment === 'production') {
    console.log('⚠️  WARNING: You are running migration in PRODUCTION!');
    console.log('Make sure you have a complete backup before proceeding.');
    
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    const answer = await new Promise((resolve) => {
      rl.question('Type "MIGRATE" to confirm you want to proceed: ', resolve);
    });
    
    rl.close();
    
    if (answer !== 'MIGRATE') {
      console.log('❌ Migration cancelled');
      process.exit(0);
    }
  }
  
  try {
    console.log('📊 Starting data migration...');
    console.log('This may take several minutes depending on your data size.');
    
    const startTime = Date.now();
    
    await DataMigration.migrateToMultiTenant();
    
    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);
    
    console.log('✅ Migration completed successfully!');
    console.log(`⏱️  Total time: ${duration} seconds`);
    console.log('');
    console.log('Next steps:');
    console.log('1. Deploy the new Firestore security rules');
    console.log('2. Test the application with different tenants');
    console.log('3. Verify data isolation between tenants');
    console.log('4. Update your application deployment');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    console.log('');
    console.log('Troubleshooting:');
    console.log('1. Check your Firebase configuration');
    console.log('2. Ensure you have proper permissions');
    console.log('3. Check the error message above for specific issues');
    console.log('4. Consider running in smaller batches if you have large datasets');
    
    process.exit(1);
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  switch (command) {
    case 'check':
      console.log('🔍 Checking migration status...');
      console.log('Migration status check completed');
      break;
    case 'migrate':
    default:
      await runMigration();
      break;
  }
}

// Handle uncaught errors
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Run the script
if (require.main === module) {
  main().catch((error) => {
    console.error('Script execution failed:', error);
    process.exit(1);
  });
}
