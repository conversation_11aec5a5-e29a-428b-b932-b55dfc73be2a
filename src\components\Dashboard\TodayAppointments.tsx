'use client';

import { useTodayAppointments } from '@/hooks/useAppointments';
import { ClockIcon, UserIcon } from '@heroicons/react/24/outline';

export default function TodayAppointments() {
  const { data: todayAppointments = [], isLoading, error } = useTodayAppointments();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'status-scheduled';
      case 'confirmed': return 'status-confirmed';
      case 'in-progress': return 'status-in-progress';
      case 'completed': return 'status-completed';
      case 'cancelled': return 'status-cancelled';
      default: return 'status-scheduled';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'scheduled': return 'Terjadwal';
      case 'confirmed': return 'Dikonfirmasi';
      case 'in-progress': return 'Berlangsung';
      case 'completed': return 'Selesai';
      case 'cancelled': return 'Dibatalkan';
      default: return status;
    }
  };

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Jadwal Hari Ini</h3>
        <span className="text-sm text-gray-500">
          {todayAppointments.length} appointment
        </span>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
          <span className="ml-2 text-gray-600">Memuat jadwal...</span>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="text-center py-8 text-red-600">
          Error memuat jadwal: {error.message}
        </div>
      )}

      {/* Appointments List */}
      {!isLoading && !error && (
        <div className="space-y-4">
          {todayAppointments.map((appointment) => (
          <div key={appointment.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-4">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                  <UserIcon className="w-5 h-5 text-primary-600" />
                </div>
              </div>
              <div>
                <p className="font-medium text-gray-900">{appointment.patientName}</p>
                <p className="text-sm text-gray-600">{appointment.type}</p>
                <p className="text-sm text-gray-500">Dr. {appointment.doctorName}</p>
              </div>
            </div>
            
            <div className="text-right">
              <div className="flex items-center text-sm text-gray-600 mb-2">
                <ClockIcon className="w-4 h-4 mr-1" />
                {appointment.time} ({appointment.duration} menit)
              </div>
              <span className={`status-badge ${getStatusColor(appointment.status)}`}>
                {getStatusText(appointment.status)}
              </span>
            </div>
          </div>
        ))}
        
        {todayAppointments.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            Tidak ada appointment hari ini
          </div>
        )}
        </div>
      )}
    </div>
  );
}
