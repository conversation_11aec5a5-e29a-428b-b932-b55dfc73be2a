'use client';

import { useState, useEffect } from 'react';
import { useCreateInvoice } from '@/hooks/useBilling';
import { usePatients } from '@/hooks/usePatients';
import { useTreatments } from '@/hooks/useTreatments';
import { Invoice } from '@/types';
import { 
  DocumentTextIcon,
  UserIcon,
  PlusIcon,
  TrashIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

interface InvoiceFormProps {
  onSuccess?: (invoiceId: string) => void;
  onCancel?: () => void;
  patientId?: string;
  appointmentId?: string;
}

interface InvoiceItem {
  description: string;
  quantity: number;
  price: number;
}

export default function InvoiceForm({ 
  onSuccess, 
  onCancel,
  patientId: initialPatientId,
  appointmentId 
}: InvoiceFormProps) {
  const createInvoiceMutation = useCreateInvoice();
  const { data: patients = [] } = usePatients();
  const { data: treatments = [] } = useTreatments();
  
  const [formData, setFormData] = useState({
    patientId: initialPatientId || '',
    patientName: '',
    date: new Date().toISOString().split('T')[0],
    discount: 0,
    status: 'draft' as const
  });

  const [items, setItems] = useState<InvoiceItem[]>([
    { description: '', quantity: 1, price: 0 }
  ]);

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Update patient name when patient is selected
  useEffect(() => {
    if (formData.patientId) {
      const selectedPatient = patients.find(p => p.id === formData.patientId);
      if (selectedPatient) {
        setFormData(prev => ({
          ...prev,
          patientName: selectedPatient.name
        }));
      }
    }
  }, [formData.patientId, patients]);

  const calculateTotals = () => {
    const subtotal = items.reduce((sum, item) => sum + (item.quantity * item.price), 0);
    const discountAmount = (subtotal * formData.discount) / 100;
    const taxableAmount = subtotal - discountAmount;
    const tax = taxableAmount * 0.11; // 11% PPN
    const total = taxableAmount + tax;

    return {
      subtotal,
      discountAmount,
      tax,
      total
    };
  };

  const { subtotal, discountAmount, tax, total } = calculateTotals();

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.patientId) {
      newErrors.patientId = 'Pilih pasien';
    }
    if (!formData.date) {
      newErrors.date = 'Tanggal invoice wajib diisi';
    }
    if (items.length === 0 || items.every(item => !item.description.trim())) {
      newErrors.items = 'Minimal satu item harus diisi';
    }
    
    items.forEach((item, index) => {
      if (item.description.trim() && (item.quantity <= 0 || item.price < 0)) {
        newErrors[`item_${index}`] = 'Quantity harus > 0 dan price harus >= 0';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleItemChange = (index: number, field: keyof InvoiceItem, value: string | number) => {
    const newItems = [...items];
    newItems[index] = {
      ...newItems[index],
      [field]: field === 'description' ? value : Number(value)
    };
    setItems(newItems);

    // Clear item error
    if (errors[`item_${index}`]) {
      setErrors(prev => ({
        ...prev,
        [`item_${index}`]: ''
      }));
    }
  };

  const addItem = () => {
    setItems([...items, { description: '', quantity: 1, price: 0 }]);
  };

  const removeItem = (index: number) => {
    if (items.length > 1) {
      setItems(items.filter((_, i) => i !== index));
    }
  };

  const addTreatmentAsItem = (treatmentId: string) => {
    const treatment = treatments.find(t => t.id === treatmentId);
    if (treatment) {
      setItems([...items, {
        description: treatment.name,
        quantity: 1,
        price: treatment.price || 0
      }]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    
    try {
      // Generate invoice number (simplified)
      const invoiceNumber = `INV-${Date.now()}`;
      
      const validItems = items.filter(item => item.description.trim());
      
      const invoiceData: Omit<Invoice, 'id'> = {
        invoiceNumber,
        patientId: formData.patientId,
        patientName: formData.patientName,
        date: formData.date,
        items: validItems,
        subtotal,
        tax,
        discount: formData.discount,
        total,
        status: formData.status,
        appointmentId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const invoiceId = await createInvoiceMutation.mutateAsync(invoiceData);
      onSuccess?.(invoiceId);
    } catch (error) {
      console.error('Error creating invoice:', error);
      setErrors({ submit: 'Gagal membuat invoice. Silakan coba lagi.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Buat Invoice Baru</h2>
            <p className="mt-1 text-gray-600">Buat invoice untuk pembayaran treatment</p>
          </div>
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="p-2 hover:bg-gray-100 rounded-lg"
            >
              <XMarkIcon className="w-6 h-6 text-gray-400" />
            </button>
          )}
        </div>

        {/* Patient & Date */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <UserIcon className="w-5 h-5 mr-2" />
            Informasi Invoice
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Pasien *
              </label>
              <select
                value={formData.patientId}
                onChange={(e) => handleInputChange('patientId', e.target.value)}
                className={`input ${errors.patientId ? 'border-red-500' : ''}`}
                disabled={!!initialPatientId}
              >
                <option value="">Pilih pasien...</option>
                {patients.map((patient) => (
                  <option key={patient.id} value={patient.id}>
                    {patient.name} - {patient.medicalRecordNumber}
                  </option>
                ))}
              </select>
              {errors.patientId && <p className="mt-1 text-sm text-red-600">{errors.patientId}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tanggal Invoice *
              </label>
              <input
                type="date"
                value={formData.date}
                onChange={(e) => handleInputChange('date', e.target.value)}
                className={`input ${errors.date ? 'border-red-500' : ''}`}
              />
              {errors.date && <p className="mt-1 text-sm text-red-600">{errors.date}</p>}
            </div>
          </div>
        </div>

        {/* Items */}
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <DocumentTextIcon className="w-5 h-5 mr-2" />
              Item Treatment
            </h3>
            <div className="flex space-x-2">
              {treatments.length > 0 && (
                <select
                  onChange={(e) => {
                    if (e.target.value) {
                      addTreatmentAsItem(e.target.value);
                      e.target.value = '';
                    }
                  }}
                  className="input text-sm"
                >
                  <option value="">Tambah dari treatment...</option>
                  {treatments.map((treatment) => (
                    <option key={treatment.id} value={treatment.id}>
                      {treatment.name} - Rp {treatment.price?.toLocaleString() || 0}
                    </option>
                  ))}
                </select>
              )}
              <button
                type="button"
                onClick={addItem}
                className="btn-secondary flex items-center space-x-1"
              >
                <PlusIcon className="w-4 h-4" />
                <span>Tambah Item</span>
              </button>
            </div>
          </div>

          <div className="space-y-3">
            {items.map((item, index) => (
              <div key={index} className="grid grid-cols-12 gap-3 items-end">
                <div className="col-span-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Deskripsi
                  </label>
                  <input
                    type="text"
                    value={item.description}
                    onChange={(e) => handleItemChange(index, 'description', e.target.value)}
                    className="input"
                    placeholder="Nama treatment/layanan"
                  />
                </div>
                <div className="col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Qty
                  </label>
                  <input
                    type="number"
                    value={item.quantity}
                    onChange={(e) => handleItemChange(index, 'quantity', e.target.value)}
                    className="input"
                    min="1"
                  />
                </div>
                <div className="col-span-3">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Harga
                  </label>
                  <input
                    type="number"
                    value={item.price}
                    onChange={(e) => handleItemChange(index, 'price', e.target.value)}
                    className="input"
                    min="0"
                    placeholder="0"
                  />
                </div>
                <div className="col-span-1">
                  <button
                    type="button"
                    onClick={() => removeItem(index)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-lg"
                    disabled={items.length === 1}
                  >
                    <TrashIcon className="w-4 h-4" />
                  </button>
                </div>
                {errors[`item_${index}`] && (
                  <div className="col-span-12">
                    <p className="text-sm text-red-600">{errors[`item_${index}`]}</p>
                  </div>
                )}
              </div>
            ))}
          </div>

          {errors.items && (
            <p className="mt-2 text-sm text-red-600">{errors.items}</p>
          )}
        </div>

        {/* Totals */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Total Pembayaran</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Diskon (%)
              </label>
              <input
                type="number"
                value={formData.discount}
                onChange={(e) => handleInputChange('discount', Number(e.target.value))}
                className="input w-32"
                min="0"
                max="100"
              />
            </div>

            <div className="border-t pt-4 space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Subtotal:</span>
                <span className="font-medium">Rp {subtotal.toLocaleString()}</span>
              </div>
              {formData.discount > 0 && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Diskon ({formData.discount}%):</span>
                  <span className="font-medium text-red-600">-Rp {discountAmount.toLocaleString()}</span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-gray-600">PPN (11%):</span>
                <span className="font-medium">Rp {tax.toLocaleString()}</span>
              </div>
              <div className="flex justify-between text-lg font-bold border-t pt-2">
                <span>Total:</span>
                <span>Rp {total.toLocaleString()}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Submit Error */}
        {errors.submit && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-600">{errors.submit}</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="btn-secondary"
              disabled={isSubmitting}
            >
              Batal
            </button>
          )}
          <button
            type="submit"
            className="btn-primary"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Menyimpan...' : 'Buat Invoice'}
          </button>
        </div>
      </form>
    </div>
  );
}
