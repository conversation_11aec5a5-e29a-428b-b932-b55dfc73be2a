'use client';

import { useState, useEffect } from 'react';
import { useCreateUser, useUpdateUser, useRoleUtils } from '@/hooks/useUsers';
import { User } from '@/types';
import { 
  UserIcon,
  EnvelopeIcon,
  KeyIcon,
  ShieldCheckIcon,
  XMarkIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline';

interface UserFormProps {
  user?: User;
  onSuccess?: (userId: string) => void;
  onCancel?: () => void;
}

export default function UserForm({ user, onSuccess, onCancel }: UserFormProps) {
  const createUserMutation = useCreateUser();
  const updateUserMutation = useUpdateUser();
  const { getRoleDisplayName, getRolePermissions, generateDefaultPassword } = useRoleUtils();
  
  const isEditing = !!user;
  
  const [formData, setFormData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    role: user?.role || 'assistant' as User['role'],
    password: '',
    confirmPassword: '',
    avatar: user?.avatar || ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const roles: User['role'][] = ['admin', 'manager', 'doctor', 'receptionist', 'assistant'];

  // Generate default password for new users
  useEffect(() => {
    if (!isEditing && !formData.password) {
      const defaultPassword = generateDefaultPassword();
      setFormData(prev => ({
        ...prev,
        password: defaultPassword,
        confirmPassword: defaultPassword
      }));
    }
  }, [isEditing, generateDefaultPassword, formData.password]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = 'Nama wajib diisi';
    if (!formData.email.trim()) newErrors.email = 'Email wajib diisi';
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Format email tidak valid';
    }
    if (!formData.role) newErrors.role = 'Role wajib dipilih';

    // Password validation for new users or when password is being changed
    if (!isEditing || formData.password) {
      if (!formData.password) newErrors.password = 'Password wajib diisi';
      if (formData.password.length < 6) newErrors.password = 'Password minimal 6 karakter';
      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Konfirmasi password tidak cocok';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setFormData(prev => ({
          ...prev,
          avatar: result
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const generateNewPassword = () => {
    const newPassword = generateDefaultPassword();
    setFormData(prev => ({
      ...prev,
      password: newPassword,
      confirmPassword: newPassword
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    
    try {
      if (isEditing && user) {
        const updates: Partial<User> = {
          name: formData.name,
          email: formData.email,
          role: formData.role,
          avatar: formData.avatar
        };

        // Only include password if it's being changed
        if (formData.password) {
          // In a real app, you'd hash the password here
          updates.password = formData.password;
        }

        await updateUserMutation.mutateAsync({ id: user.id, updates });
        onSuccess?.(user.id);
      } else {
        const userData: Omit<User, 'id'> = {
          name: formData.name,
          email: formData.email,
          role: formData.role,
          tenantId: '', // Will be set by the service
          avatar: formData.avatar,
          password: formData.password, // In a real app, you'd hash this
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        const userId = await createUserMutation.mutateAsync(userData);
        onSuccess?.(userId);
      }
    } catch (error) {
      console.error('Error saving user:', error);
      setErrors({ submit: 'Gagal menyimpan user. Silakan coba lagi.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              {isEditing ? 'Edit User' : 'Tambah User Baru'}
            </h2>
            <p className="mt-1 text-gray-600">
              {isEditing ? 'Update informasi user' : 'Tambahkan staff baru ke sistem'}
            </p>
          </div>
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="p-2 hover:bg-gray-100 rounded-lg"
            >
              <XMarkIcon className="w-6 h-6 text-gray-400" />
            </button>
          )}
        </div>

        {/* Avatar */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Foto Profil</h3>
          
          <div className="flex items-center space-x-6">
            <div className="w-24 h-24 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
              {formData.avatar ? (
                <img 
                  src={formData.avatar} 
                  alt="Avatar" 
                  className="w-full h-full object-cover"
                />
              ) : (
                <UserIcon className="w-12 h-12 text-gray-400" />
              )}
            </div>
            <div>
              <label className="btn-secondary cursor-pointer">
                Upload Foto
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />
              </label>
              <p className="text-sm text-gray-500 mt-2">
                JPG, PNG atau GIF. Maksimal 2MB.
              </p>
            </div>
          </div>
        </div>

        {/* Basic Information */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <UserIcon className="w-5 h-5 mr-2" />
            Informasi Dasar
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nama Lengkap *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={`input ${errors.name ? 'border-red-500' : ''}`}
                placeholder="Masukkan nama lengkap"
              />
              {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email *
              </label>
              <div className="relative">
                <EnvelopeIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className={`input pl-10 ${errors.email ? 'border-red-500' : ''}`}
                  placeholder="<EMAIL>"
                />
              </div>
              {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email}</p>}
            </div>
          </div>
        </div>

        {/* Role & Permissions */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <ShieldCheckIcon className="w-5 h-5 mr-2" />
            Role & Permissions
          </h3>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Role *
            </label>
            <select
              value={formData.role}
              onChange={(e) => handleInputChange('role', e.target.value)}
              className={`input ${errors.role ? 'border-red-500' : ''}`}
            >
              {roles.map((role) => (
                <option key={role} value={role}>
                  {getRoleDisplayName(role)}
                </option>
              ))}
            </select>
            {errors.role && <p className="mt-1 text-sm text-red-600">{errors.role}</p>}
          </div>

          {/* Role Permissions */}
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-700 mb-2">
              Permissions untuk {getRoleDisplayName(formData.role)}:
            </h4>
            <ul className="text-sm text-gray-600 space-y-1">
              {getRolePermissions(formData.role).map((permission, index) => (
                <li key={index} className="flex items-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  {permission}
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Password */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <KeyIcon className="w-5 h-5 mr-2" />
            {isEditing ? 'Ubah Password (Opsional)' : 'Password'}
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Password {!isEditing && '*'}
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  className={`input pr-10 ${errors.password ? 'border-red-500' : ''}`}
                  placeholder={isEditing ? 'Kosongkan jika tidak ingin mengubah' : 'Masukkan password'}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeSlashIcon className="w-5 h-5" /> : <EyeIcon className="w-5 h-5" />}
                </button>
              </div>
              {errors.password && <p className="mt-1 text-sm text-red-600">{errors.password}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Konfirmasi Password {!isEditing && '*'}
              </label>
              <div className="relative">
                <input
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={formData.confirmPassword}
                  onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                  className={`input pr-10 ${errors.confirmPassword ? 'border-red-500' : ''}`}
                  placeholder="Konfirmasi password"
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showConfirmPassword ? <EyeSlashIcon className="w-5 h-5" /> : <EyeIcon className="w-5 h-5" />}
                </button>
              </div>
              {errors.confirmPassword && <p className="mt-1 text-sm text-red-600">{errors.confirmPassword}</p>}
            </div>

            <button
              type="button"
              onClick={generateNewPassword}
              className="btn-secondary text-sm"
            >
              Generate Password Baru
            </button>
          </div>
        </div>

        {/* Submit Error */}
        {errors.submit && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-600">{errors.submit}</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="btn-secondary"
              disabled={isSubmitting}
            >
              Batal
            </button>
          )}
          <button
            type="submit"
            className="btn-primary"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Menyimpan...' : isEditing ? 'Update User' : 'Tambah User'}
          </button>
        </div>
      </form>
    </div>
  );
}
