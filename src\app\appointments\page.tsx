'use client';

import { useState } from 'react';
import Header from '@/components/Layout/Header';
import AppointmentCalendar from '@/components/Appointments/AppointmentCalendar';
import AppointmentBookingForm from '@/components/Appointments/AppointmentBookingForm';
import { Appointment } from '@/types';

export default function AppointmentsPage() {
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);
  const [showBookingForm, setShowBookingForm] = useState(false);

  const handleBookingSuccess = (appointmentId: string) => {
    setShowBookingForm(false);
    console.log('Appointment created with ID:', appointmentId);
  };

  if (showBookingForm) {
    return (
      <div className="flex-1 overflow-auto">
        <Header
          title="Buat Appointment Baru"
          subtitle="Jadwalkan appointment untuk pasien"
        />

        <main className="p-6">
          <AppointmentBookingForm
            onSuccess={handleBookingSuccess}
            onCancel={() => setShowBookingForm(false)}
          />
        </main>
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-auto">
      <Header 
        title="Manajemen Jadwal" 
        subtitle="Kelola appointment dan jadwal dokter"
      />
      
      <main className="p-6">
        <AppointmentCalendar
          onSelectAppointment={setSelectedAppointment}
          onCreateAppointment={() => setShowBookingForm(true)}
        />
        
        {selectedAppointment && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-2xl w-full p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-gray-900">Detail Appointment</h3>
                <button 
                  onClick={() => setSelectedAppointment(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>
              
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Pasien</label>
                    <p className="text-lg font-medium">{selectedAppointment.patientName}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Dokter</label>
                    <p className="text-lg font-medium">Dr. {selectedAppointment.doctorName}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Tanggal & Waktu</label>
                    <p className="text-lg font-medium">
                      {new Date(selectedAppointment.date).toLocaleDateString('id-ID')} - {selectedAppointment.time}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Durasi</label>
                    <p className="text-lg font-medium">{selectedAppointment.duration} menit</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Jenis Treatment</label>
                    <p className="text-lg font-medium">{selectedAppointment.type}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Status</label>
                    <span className={`status-badge ${
                      selectedAppointment.status === 'scheduled' ? 'status-scheduled' :
                      selectedAppointment.status === 'confirmed' ? 'status-confirmed' :
                      selectedAppointment.status === 'in-progress' ? 'status-in-progress' :
                      selectedAppointment.status === 'completed' ? 'status-completed' :
                      'status-cancelled'
                    }`}>
                      {selectedAppointment.status === 'scheduled' ? 'Terjadwal' :
                       selectedAppointment.status === 'confirmed' ? 'Dikonfirmasi' :
                       selectedAppointment.status === 'in-progress' ? 'Berlangsung' :
                       selectedAppointment.status === 'completed' ? 'Selesai' :
                       'Dibatalkan'}
                    </span>
                  </div>
                </div>
                
                {selectedAppointment.notes && (
                  <div>
                    <label className="text-sm font-medium text-gray-600">Catatan</label>
                    <p className="text-gray-900 bg-gray-50 p-3 rounded-lg">{selectedAppointment.notes}</p>
                  </div>
                )}
                
                {selectedAppointment.treatmentPlan && selectedAppointment.treatmentPlan.length > 0 && (
                  <div>
                    <label className="text-sm font-medium text-gray-600">Rencana Treatment</label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {selectedAppointment.treatmentPlan.map((treatment, index) => (
                        <span key={index} className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm">
                          {treatment}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              
              <div className="flex justify-end space-x-3 mt-6">
                <button className="btn-secondary">Edit</button>
                <button className="btn-danger">Batalkan</button>
                <button className="btn-primary">Mulai Treatment</button>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
