'use client';

import { useState } from 'react';
import Header from '@/components/Layout/Header';
import PatientList from '@/components/Patients/PatientList';
import PatientDetail from '@/components/Patients/PatientDetail';
import PatientRegistrationForm from '@/components/Patients/PatientRegistrationForm';
import { Patient } from '@/types';
import { PlusIcon } from '@heroicons/react/24/outline';

export default function PatientsPage() {
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [showRegistrationForm, setShowRegistrationForm] = useState(false);

  const handleRegistrationSuccess = (patientId: string) => {
    setShowRegistrationForm(false);
    // Optionally, you could navigate to the patient detail page
    console.log('Patient created with ID:', patientId);
  };

  if (showRegistrationForm) {
    return (
      <div className="flex-1 overflow-auto">
        <Header
          title="Registrasi Pasien Baru"
          subtitle="Tambahkan pasien baru ke sistem"
        />

        <main className="p-6">
          <PatientRegistrationForm
            onSuccess={handleRegistrationSuccess}
            onCancel={() => setShowRegistrationForm(false)}
          />
        </main>
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-auto">
      <Header
        title="Manajemen Pasien"
        subtitle="Kelola data pasien dan rekam medis"
        action={
          <button
            onClick={() => setShowRegistrationForm(true)}
            className="btn-primary flex items-center space-x-2"
          >
            <PlusIcon className="w-5 h-5" />
            <span>Tambah Pasien</span>
          </button>
        }
      />

      <main className="p-6">
        <PatientList onSelectPatient={setSelectedPatient} />

        {selectedPatient && (
          <PatientDetail
            patient={selectedPatient}
            onClose={() => setSelectedPatient(null)}
          />
        )}
      </main>
    </div>
  );
}
