import { 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  onSnapshot
} from 'firebase/firestore';
import { User } from '@/types';
import { TenantService } from './base/TenantService';

export class UserService extends TenantService {

  /**
   * Create new user
   */
  async createUser(userData: Omit<User, 'id'>): Promise<string> {
    try {
      this.validateTenantAccess();
      
      // Check if email already exists
      const existingUser = await this.getUserByEmail(userData.email);
      if (existingUser) {
        throw new Error('Email sudah digunakan oleh user lain');
      }
      
      const newUser: Omit<User, 'id'> = {
        ...userData,
        tenantId: this.tenantId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      const docRef = await addDoc(this.getCollection('users'), newUser);
      return docRef.id;
    } catch (error) {
      this.handleError(error, 'create user');
    }
  }

  /**
   * Get user by ID
   */
  async getUser(id: string): Promise<User | null> {
    try {
      this.validateTenantAccess();
      const docRef = this.getDocument('users', id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as User;
      }
      return null;
    } catch (error) {
      this.handleError(error, 'get user');
    }
  }

  /**
   * Get user by email
   */
  async getUserByEmail(email: string): Promise<User | null> {
    try {
      this.validateTenantAccess();
      const q = query(
        this.getCollection('users'),
        where('email', '==', email),
        where('tenantId', '==', this.tenantId)
      );
      
      const querySnapshot = await getDocs(q);
      if (!querySnapshot.empty) {
        const doc = querySnapshot.docs[0];
        return { id: doc.id, ...doc.data() } as User;
      }
      return null;
    } catch (error) {
      this.handleError(error, 'get user by email');
    }
  }

  /**
   * Get all users for current tenant
   */
  async getUsers(): Promise<User[]> {
    try {
      this.validateTenantAccess();
      const q = query(
        this.getCollection('users'),
        where('tenantId', '==', this.tenantId),
        orderBy('name')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as User[];
    } catch (error) {
      this.handleError(error, 'get users');
    }
  }

  /**
   * Get users by role
   */
  async getUsersByRole(role: User['role']): Promise<User[]> {
    try {
      this.validateTenantAccess();
      const q = query(
        this.getCollection('users'),
        where('tenantId', '==', this.tenantId),
        where('role', '==', role),
        orderBy('name')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as User[];
    } catch (error) {
      this.handleError(error, 'get users by role');
    }
  }

  /**
   * Update user
   */
  async updateUser(id: string, updates: Partial<User>): Promise<void> {
    try {
      this.validateTenantAccess();
      
      // If email is being updated, check for duplicates
      if (updates.email) {
        const existingUser = await this.getUserByEmail(updates.email);
        if (existingUser && existingUser.id !== id) {
          throw new Error('Email sudah digunakan oleh user lain');
        }
      }
      
      const docRef = this.getDocument('users', id);
      await updateDoc(docRef, {
        ...updates,
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      this.handleError(error, 'update user');
    }
  }

  /**
   * Delete user
   */
  async deleteUser(id: string): Promise<void> {
    try {
      this.validateTenantAccess();
      const docRef = this.getDocument('users', id);
      await deleteDoc(docRef);
    } catch (error) {
      this.handleError(error, 'delete user');
    }
  }

  /**
   * Search users
   */
  async searchUsers(searchTerm: string): Promise<User[]> {
    try {
      this.validateTenantAccess();
      const users = await this.getUsers();
      
      const lowercaseSearch = searchTerm.toLowerCase();
      return users.filter(user => 
        user.name.toLowerCase().includes(lowercaseSearch) ||
        user.email.toLowerCase().includes(lowercaseSearch)
      );
    } catch (error) {
      this.handleError(error, 'search users');
    }
  }

  /**
   * Get user statistics
   */
  async getUserStats(): Promise<{
    totalUsers: number;
    usersByRole: Record<User['role'], number>;
    activeUsers: number;
  }> {
    try {
      this.validateTenantAccess();
      const users = await this.getUsers();
      
      const usersByRole = users.reduce((acc, user) => {
        acc[user.role] = (acc[user.role] || 0) + 1;
        return acc;
      }, {} as Record<User['role'], number>);

      // For now, consider all users as active
      // In a real implementation, you might track last login, etc.
      const activeUsers = users.length;

      return {
        totalUsers: users.length,
        usersByRole,
        activeUsers
      };
    } catch (error) {
      this.handleError(error, 'get user stats');
    }
  }

  /**
   * Subscribe to users changes
   */
  subscribeToUsers(callback: (users: User[]) => void): () => void {
    this.validateTenantAccess();
    
    const q = query(
      this.getCollection('users'),
      where('tenantId', '==', this.tenantId),
      orderBy('name')
    );
    
    return onSnapshot(q, (snapshot) => {
      const users = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as User[];
      callback(users);
    });
  }

  /**
   * Validate user permissions
   */
  validateUserPermissions(user: User, requiredRole: User['role'][]): boolean {
    return requiredRole.includes(user.role);
  }

  /**
   * Get role hierarchy (for permission checking)
   */
  getRoleHierarchy(): Record<User['role'], number> {
    return {
      'admin': 5,
      'manager': 4,
      'doctor': 3,
      'receptionist': 2,
      'assistant': 1
    };
  }

  /**
   * Check if user has higher or equal role
   */
  hasPermission(userRole: User['role'], requiredRole: User['role']): boolean {
    const hierarchy = this.getRoleHierarchy();
    return hierarchy[userRole] >= hierarchy[requiredRole];
  }

  /**
   * Generate default password (should be changed on first login)
   */
  generateDefaultPassword(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let password = '';
    for (let i = 0; i < 8; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
  }

  /**
   * Get role display name
   */
  getRoleDisplayName(role: User['role']): string {
    const roleNames: Record<User['role'], string> = {
      'admin': 'Administrator',
      'manager': 'Manajer',
      'doctor': 'Dokter',
      'receptionist': 'Resepsionis',
      'assistant': 'Asisten'
    };
    return roleNames[role] || role;
  }

  /**
   * Get role permissions description
   */
  getRolePermissions(role: User['role']): string[] {
    const permissions: Record<User['role'], string[]> = {
      'admin': [
        'Akses penuh ke semua fitur',
        'Mengelola user dan pengaturan',
        'Melihat semua laporan',
        'Backup dan restore data'
      ],
      'manager': [
        'Mengelola operasional klinik',
        'Melihat laporan keuangan',
        'Mengelola jadwal dokter',
        'Approve treatment mahal'
      ],
      'doctor': [
        'Mengelola pasien dan rekam medis',
        'Melakukan treatment',
        'Melihat jadwal sendiri',
        'Input diagnosis dan resep'
      ],
      'receptionist': [
        'Registrasi pasien baru',
        'Booking appointment',
        'Mengelola billing',
        'Komunikasi dengan pasien'
      ],
      'assistant': [
        'Membantu treatment',
        'Mengelola inventory',
        'Input data dasar',
        'Melihat jadwal'
      ]
    };
    return permissions[role] || [];
  }
}
