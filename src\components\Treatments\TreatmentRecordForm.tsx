'use client';

import { useState, useEffect } from 'react';
import { usePatients, useUpdateDentalChart } from '@/hooks/usePatients';
import { useTreatments } from '@/hooks/useTreatments';
import { useCreateInvoiceFromAppointment } from '@/hooks/useBilling';
import DentalChart from '@/components/Patients/DentalChart';
import { Patient, Treatment } from '@/types';
import { 
  UserIcon,
  ClipboardDocumentListIcon,
  CurrencyDollarIcon,
  PhotoIcon,
  XMarkIcon,
  PlusIcon,
  TrashIcon
} from '@heroicons/react/24/outline';

interface TreatmentRecordFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  appointmentId?: string;
  patientId?: string;
}

interface TreatmentItem {
  treatmentId: string;
  treatmentName: string;
  toothNumbers: number[];
  notes: string;
  price: number;
  duration: number;
}

export default function TreatmentRecordForm({ 
  onSuccess, 
  onCancel,
  appointmentId,
  patientId: initialPatientId 
}: TreatmentRecordFormProps) {
  const { data: patients = [] } = usePatients();
  const { data: treatments = [] } = useTreatments();
  const updateDentalChartMutation = useUpdateDentalChart();
  const createInvoiceMutation = useCreateInvoiceFromAppointment();
  
  const [formData, setFormData] = useState({
    patientId: initialPatientId || '',
    doctorName: 'Dr. Sari Wijaya',
    date: new Date().toISOString().split('T')[0],
    startTime: new Date().toTimeString().slice(0, 5),
    endTime: '',
    notes: '',
    followUpDate: '',
    createInvoice: true
  });

  const [treatmentItems, setTreatmentItems] = useState<TreatmentItem[]>([]);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [clinicalImages, setClinicalImages] = useState<string[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Available doctors
  const doctors = [
    'Dr. Sari Wijaya',
    'Dr. Budi Santoso',
    'Dr. Maya Putri',
    'Dr. Ahmad Rahman'
  ];

  // Update selected patient when patientId changes
  useEffect(() => {
    if (formData.patientId) {
      const patient = patients.find(p => p.id === formData.patientId);
      setSelectedPatient(patient || null);
    }
  }, [formData.patientId, patients]);

  // Calculate end time based on treatment durations
  useEffect(() => {
    if (formData.startTime && treatmentItems.length > 0) {
      const totalDuration = treatmentItems.reduce((sum, item) => sum + item.duration, 0);
      const startTime = new Date(`2000-01-01T${formData.startTime}`);
      const endTime = new Date(startTime.getTime() + totalDuration * 60000);
      setFormData(prev => ({
        ...prev,
        endTime: endTime.toTimeString().slice(0, 5)
      }));
    }
  }, [formData.startTime, treatmentItems]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.patientId) newErrors.patientId = 'Pilih pasien';
    if (!formData.doctorName) newErrors.doctorName = 'Pilih dokter';
    if (!formData.date) newErrors.date = 'Tanggal treatment wajib diisi';
    if (!formData.startTime) newErrors.startTime = 'Waktu mulai wajib diisi';
    if (treatmentItems.length === 0) newErrors.treatments = 'Minimal satu treatment harus dipilih';

    treatmentItems.forEach((item, index) => {
      if (!item.treatmentName.trim()) {
        newErrors[`treatment_${index}`] = 'Nama treatment wajib diisi';
      }
      if (item.price < 0) {
        newErrors[`treatment_${index}_price`] = 'Harga tidak boleh negatif';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const addTreatmentItem = () => {
    setTreatmentItems([...treatmentItems, {
      treatmentId: '',
      treatmentName: '',
      toothNumbers: [],
      notes: '',
      price: 0,
      duration: 30
    }]);
  };

  const removeTreatmentItem = (index: number) => {
    setTreatmentItems(treatmentItems.filter((_, i) => i !== index));
  };

  const updateTreatmentItem = (index: number, field: keyof TreatmentItem, value: any) => {
    const newItems = [...treatmentItems];
    newItems[index] = {
      ...newItems[index],
      [field]: value
    };
    setTreatmentItems(newItems);
  };

  const handleTreatmentSelect = (index: number, treatmentId: string) => {
    const treatment = treatments.find(t => t.id === treatmentId);
    if (treatment) {
      updateTreatmentItem(index, 'treatmentId', treatmentId);
      updateTreatmentItem(index, 'treatmentName', treatment.name);
      updateTreatmentItem(index, 'price', treatment.price || 0);
      updateTreatmentItem(index, 'duration', treatment.estimatedDuration || 30);
    }
  };

  const handleToothUpdate = async (toothNumber: number, condition: string, notes?: string) => {
    if (!selectedPatient) return;
    
    try {
      await updateDentalChartMutation.mutateAsync({
        patientId: selectedPatient.id,
        toothNumber,
        condition: condition as any,
        notes
      });
    } catch (error) {
      console.error('Error updating dental chart:', error);
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      Array.from(files).forEach(file => {
        const reader = new FileReader();
        reader.onload = (e) => {
          const result = e.target?.result as string;
          setClinicalImages(prev => [...prev, result]);
        };
        reader.readAsDataURL(file);
      });
    }
  };

  const removeImage = (index: number) => {
    setClinicalImages(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    
    try {
      // Create invoice if requested
      if (formData.createInvoice && selectedPatient) {
        const invoiceTreatments = treatmentItems.map(item => ({
          name: item.treatmentName,
          price: item.price,
          quantity: 1
        }));

        await createInvoiceMutation.mutateAsync({
          appointmentId: appointmentId || '',
          patientId: selectedPatient.id,
          patientName: selectedPatient.name,
          treatments: invoiceTreatments
        });
      }

      // Here you would typically save the treatment record
      // For now, we'll just call onSuccess
      onSuccess?.();
    } catch (error) {
      console.error('Error saving treatment record:', error);
      setErrors({ submit: 'Gagal menyimpan record treatment. Silakan coba lagi.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-6xl mx-auto">
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Record Treatment</h2>
            <p className="mt-1 text-gray-600">Catat treatment yang telah dilakukan</p>
          </div>
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="p-2 hover:bg-gray-100 rounded-lg"
            >
              <XMarkIcon className="w-6 h-6 text-gray-400" />
            </button>
          )}
        </div>

        {/* Patient & Basic Info */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <UserIcon className="w-5 h-5 mr-2" />
            Informasi Dasar
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Pasien *
              </label>
              <select
                value={formData.patientId}
                onChange={(e) => handleInputChange('patientId', e.target.value)}
                className={`input ${errors.patientId ? 'border-red-500' : ''}`}
                disabled={!!initialPatientId}
              >
                <option value="">Pilih pasien...</option>
                {patients.map((patient) => (
                  <option key={patient.id} value={patient.id}>
                    {patient.name} - {patient.medicalRecordNumber}
                  </option>
                ))}
              </select>
              {errors.patientId && <p className="mt-1 text-sm text-red-600">{errors.patientId}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Dokter *
              </label>
              <select
                value={formData.doctorName}
                onChange={(e) => handleInputChange('doctorName', e.target.value)}
                className={`input ${errors.doctorName ? 'border-red-500' : ''}`}
              >
                {doctors.map((doctor) => (
                  <option key={doctor} value={doctor}>
                    {doctor}
                  </option>
                ))}
              </select>
              {errors.doctorName && <p className="mt-1 text-sm text-red-600">{errors.doctorName}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tanggal Treatment *
              </label>
              <input
                type="date"
                value={formData.date}
                onChange={(e) => handleInputChange('date', e.target.value)}
                className={`input ${errors.date ? 'border-red-500' : ''}`}
              />
              {errors.date && <p className="mt-1 text-sm text-red-600">{errors.date}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Waktu Mulai *
              </label>
              <input
                type="time"
                value={formData.startTime}
                onChange={(e) => handleInputChange('startTime', e.target.value)}
                className={`input ${errors.startTime ? 'border-red-500' : ''}`}
              />
              {errors.startTime && <p className="mt-1 text-sm text-red-600">{errors.startTime}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Waktu Selesai
              </label>
              <input
                type="time"
                value={formData.endTime}
                onChange={(e) => handleInputChange('endTime', e.target.value)}
                className="input"
                readOnly
              />
              <p className="mt-1 text-xs text-gray-500">Otomatis dihitung dari durasi treatment</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Follow-up Date
              </label>
              <input
                type="date"
                value={formData.followUpDate}
                onChange={(e) => handleInputChange('followUpDate', e.target.value)}
                className="input"
                min={formData.date}
              />
            </div>
          </div>
        </div>

        {/* Treatment Items */}
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <ClipboardDocumentListIcon className="w-5 h-5 mr-2" />
              Treatment yang Dilakukan
            </h3>
            <button
              type="button"
              onClick={addTreatmentItem}
              className="btn-secondary flex items-center space-x-1"
            >
              <PlusIcon className="w-4 h-4" />
              <span>Tambah Treatment</span>
            </button>
          </div>

          {treatmentItems.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <ClipboardDocumentListIcon className="w-12 h-12 mx-auto mb-4 text-gray-400" />
              <p>Belum ada treatment yang ditambahkan</p>
              <button
                type="button"
                onClick={addTreatmentItem}
                className="btn-primary mt-4"
              >
                Tambah Treatment Pertama
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              {treatmentItems.map((item, index) => (
                <div key={index} className="border rounded-lg p-4 bg-gray-50">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="lg:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Treatment
                      </label>
                      <div className="flex space-x-2">
                        <select
                          value={item.treatmentId}
                          onChange={(e) => handleTreatmentSelect(index, e.target.value)}
                          className="input flex-1"
                        >
                          <option value="">Pilih treatment...</option>
                          {treatments.map((treatment) => (
                            <option key={treatment.id} value={treatment.id}>
                              {treatment.name}
                            </option>
                          ))}
                        </select>
                        <input
                          type="text"
                          value={item.treatmentName}
                          onChange={(e) => updateTreatmentItem(index, 'treatmentName', e.target.value)}
                          className="input flex-1"
                          placeholder="Atau ketik manual"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Harga (Rp)
                      </label>
                      <input
                        type="number"
                        value={item.price}
                        onChange={(e) => updateTreatmentItem(index, 'price', Number(e.target.value))}
                        className="input"
                        min="0"
                      />
                    </div>

                    <div className="flex items-end">
                      <button
                        type="button"
                        onClick={() => removeTreatmentItem(index)}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-lg"
                      >
                        <TrashIcon className="w-4 h-4" />
                      </button>
                    </div>

                    <div className="lg:col-span-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Catatan Treatment
                      </label>
                      <textarea
                        value={item.notes}
                        onChange={(e) => updateTreatmentItem(index, 'notes', e.target.value)}
                        className="input min-h-[60px]"
                        placeholder="Catatan detail treatment..."
                        rows={2}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {errors.treatments && (
            <p className="mt-2 text-sm text-red-600">{errors.treatments}</p>
          )}
        </div>

        {/* Dental Chart */}
        {selectedPatient && (
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Dental Chart - {selectedPatient.name}
            </h3>
            <DentalChart 
              patient={selectedPatient}
              onToothUpdate={handleToothUpdate}
            />
          </div>
        )}

        {/* Clinical Images */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <PhotoIcon className="w-5 h-5 mr-2" />
            Foto Klinis
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Upload Foto
              </label>
              <input
                type="file"
                accept="image/*"
                multiple
                onChange={handleImageUpload}
                className="input"
              />
            </div>

            {clinicalImages.length > 0 && (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {clinicalImages.map((image, index) => (
                  <div key={index} className="relative">
                    <img
                      src={image}
                      alt={`Clinical ${index + 1}`}
                      className="w-full h-32 object-cover rounded-lg border"
                    />
                    <button
                      type="button"
                      onClick={() => removeImage(index)}
                      className="absolute top-2 right-2 p-1 bg-red-600 text-white rounded-full hover:bg-red-700"
                    >
                      <XMarkIcon className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Notes & Invoice */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Catatan & Billing
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Catatan Umum
              </label>
              <textarea
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                className="input min-h-[100px]"
                placeholder="Catatan umum treatment, kondisi pasien, dll..."
                rows={4}
              />
            </div>

            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                id="createInvoice"
                checked={formData.createInvoice}
                onChange={(e) => handleInputChange('createInvoice', e.target.checked)}
                className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
              />
              <label htmlFor="createInvoice" className="text-sm font-medium text-gray-700 flex items-center">
                <CurrencyDollarIcon className="w-4 h-4 mr-1" />
                Buat invoice otomatis
              </label>
            </div>

            {formData.createInvoice && treatmentItems.length > 0 && (
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-blue-900 mb-2">Preview Invoice</h4>
                <div className="space-y-1">
                  {treatmentItems.map((item, index) => (
                    <div key={index} className="flex justify-between text-sm">
                      <span className="text-blue-800">{item.treatmentName || 'Treatment'}</span>
                      <span className="text-blue-900 font-medium">
                        Rp {item.price.toLocaleString()}
                      </span>
                    </div>
                  ))}
                  <div className="border-t border-blue-200 pt-2 mt-2">
                    <div className="flex justify-between text-sm font-medium">
                      <span className="text-blue-900">Total:</span>
                      <span className="text-blue-900">
                        Rp {treatmentItems.reduce((sum, item) => sum + item.price, 0).toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Submit Error */}
        {errors.submit && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-600">{errors.submit}</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="btn-secondary"
              disabled={isSubmitting}
            >
              Batal
            </button>
          )}
          <button
            type="submit"
            className="btn-primary"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Menyimpan...' : 'Simpan Treatment Record'}
          </button>
        </div>
      </form>
    </div>
  );
}
