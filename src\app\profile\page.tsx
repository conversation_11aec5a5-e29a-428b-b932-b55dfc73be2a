'use client';

import { useState } from 'react';
import Header from '@/components/Layout/Header';
import { 
  UserIcon,
  EnvelopeIcon,
  KeyIcon,
  PhotoIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

export default function ProfilePage() {
  const [isEditing, setIsEditing] = useState(false);
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  
  // Mock user data - in real app, this would come from auth context
  const [userData, setUserData] = useState({
    name: 'Dr. <PERSON>',
    email: '<EMAIL>',
    role: 'Do<PERSON><PERSON>igi',
    phone: '08123456789',
    avatar: null,
    joinDate: '2023-01-15'
  });

  const [editData, setEditData] = useState(userData);
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const handleSaveProfile = () => {
    // In real app, this would call an API to update user profile
    setUserData(editData);
    setIsEditing(false);
    alert('Profil berhasil diperbarui!');
  };

  const handleCancelEdit = () => {
    setEditData(userData);
    setIsEditing(false);
  };

  const handleChangePassword = () => {
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      alert('Password baru dan konfirmasi password tidak cocok!');
      return;
    }
    if (passwordData.newPassword.length < 6) {
      alert('Password baru minimal 6 karakter!');
      return;
    }
    
    // In real app, this would call an API to change password
    alert('Password berhasil diubah!');
    setPasswordData({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
    setIsChangingPassword(false);
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setEditData(prev => ({
          ...prev,
          avatar: result
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className="flex-1 overflow-auto">
      <Header 
        title="Profil Saya" 
        subtitle="Kelola informasi profil dan pengaturan akun"
      />
      
      <main className="p-6">
        <div className="max-w-2xl mx-auto space-y-6">
          {/* Profile Information */}
          <div className="card">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <UserIcon className="w-5 h-5 mr-2" />
                Informasi Profil
              </h3>
              {!isEditing ? (
                <button
                  onClick={() => setIsEditing(true)}
                  className="btn-secondary"
                >
                  Edit Profil
                </button>
              ) : (
                <div className="flex space-x-2">
                  <button
                    onClick={handleSaveProfile}
                    className="btn-primary flex items-center space-x-1"
                  >
                    <CheckIcon className="w-4 h-4" />
                    <span>Simpan</span>
                  </button>
                  <button
                    onClick={handleCancelEdit}
                    className="btn-secondary flex items-center space-x-1"
                  >
                    <XMarkIcon className="w-4 h-4" />
                    <span>Batal</span>
                  </button>
                </div>
              )}
            </div>

            <div className="space-y-6">
              {/* Avatar */}
              <div className="flex items-center space-x-6">
                <div className="w-24 h-24 rounded-full bg-primary-100 flex items-center justify-center overflow-hidden">
                  {(isEditing ? editData.avatar : userData.avatar) ? (
                    <img 
                      src={isEditing ? editData.avatar : userData.avatar} 
                      alt="Avatar" 
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <UserIcon className="w-12 h-12 text-primary-600" />
                  )}
                </div>
                {isEditing && (
                  <div>
                    <label className="btn-secondary cursor-pointer">
                      <PhotoIcon className="w-4 h-4 mr-2" />
                      Upload Foto
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                      />
                    </label>
                    <p className="text-sm text-gray-500 mt-2">
                      JPG, PNG atau GIF. Maksimal 2MB.
                    </p>
                  </div>
                )}
              </div>

              {/* Form Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nama Lengkap
                  </label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={editData.name}
                      onChange={(e) => setEditData(prev => ({ ...prev, name: e.target.value }))}
                      className="input"
                    />
                  ) : (
                    <p className="text-gray-900 py-2">{userData.name}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email
                  </label>
                  {isEditing ? (
                    <input
                      type="email"
                      value={editData.email}
                      onChange={(e) => setEditData(prev => ({ ...prev, email: e.target.value }))}
                      className="input"
                    />
                  ) : (
                    <p className="text-gray-900 py-2">{userData.email}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nomor Telepon
                  </label>
                  {isEditing ? (
                    <input
                      type="tel"
                      value={editData.phone}
                      onChange={(e) => setEditData(prev => ({ ...prev, phone: e.target.value }))}
                      className="input"
                    />
                  ) : (
                    <p className="text-gray-900 py-2">{userData.phone}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Role
                  </label>
                  <p className="text-gray-900 py-2">{userData.role}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Bergabung Sejak
                  </label>
                  <p className="text-gray-900 py-2">
                    {new Date(userData.joinDate).toLocaleDateString('id-ID', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Change Password */}
          <div className="card">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <KeyIcon className="w-5 h-5 mr-2" />
                Ubah Password
              </h3>
              {!isChangingPassword ? (
                <button
                  onClick={() => setIsChangingPassword(true)}
                  className="btn-secondary"
                >
                  Ubah Password
                </button>
              ) : (
                <div className="flex space-x-2">
                  <button
                    onClick={handleChangePassword}
                    className="btn-primary flex items-center space-x-1"
                  >
                    <CheckIcon className="w-4 h-4" />
                    <span>Simpan</span>
                  </button>
                  <button
                    onClick={() => {
                      setIsChangingPassword(false);
                      setPasswordData({
                        currentPassword: '',
                        newPassword: '',
                        confirmPassword: ''
                      });
                    }}
                    className="btn-secondary flex items-center space-x-1"
                  >
                    <XMarkIcon className="w-4 h-4" />
                    <span>Batal</span>
                  </button>
                </div>
              )}
            </div>

            {isChangingPassword ? (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Password Saat Ini
                  </label>
                  <input
                    type="password"
                    value={passwordData.currentPassword}
                    onChange={(e) => setPasswordData(prev => ({ ...prev, currentPassword: e.target.value }))}
                    className="input"
                    placeholder="Masukkan password saat ini"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Password Baru
                  </label>
                  <input
                    type="password"
                    value={passwordData.newPassword}
                    onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                    className="input"
                    placeholder="Masukkan password baru"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Konfirmasi Password Baru
                  </label>
                  <input
                    type="password"
                    value={passwordData.confirmPassword}
                    onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                    className="input"
                    placeholder="Konfirmasi password baru"
                  />
                </div>
              </div>
            ) : (
              <p className="text-gray-600">
                Untuk keamanan akun, ubah password secara berkala.
              </p>
            )}
          </div>

          {/* Account Information */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Informasi Akun
            </h3>
            
            <div className="space-y-4">
              <div className="flex justify-between items-center py-3 border-b border-gray-200">
                <div>
                  <p className="font-medium text-gray-900">Status Akun</p>
                  <p className="text-sm text-gray-600">Akun Anda saat ini aktif</p>
                </div>
                <span className="status-badge bg-green-100 text-green-800">Aktif</span>
              </div>

              <div className="flex justify-between items-center py-3 border-b border-gray-200">
                <div>
                  <p className="font-medium text-gray-900">Login Terakhir</p>
                  <p className="text-sm text-gray-600">Informasi login terakhir</p>
                </div>
                <span className="text-sm text-gray-500">
                  {new Date().toLocaleDateString('id-ID')} - {new Date().toLocaleTimeString('id-ID')}
                </span>
              </div>

              <div className="flex justify-between items-center py-3">
                <div>
                  <p className="font-medium text-gray-900">Keamanan</p>
                  <p className="text-sm text-gray-600">Pengaturan keamanan akun</p>
                </div>
                <button className="text-primary-600 hover:text-primary-700 text-sm font-medium">
                  Kelola Keamanan
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
