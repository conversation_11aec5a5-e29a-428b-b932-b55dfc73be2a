'use client';

import { useState } from 'react';
import { useAppointments } from '@/hooks/useAppointments';
import { Appointment } from '@/types';
import { 
  ChevronLeftIcon, 
  ChevronRightIcon,
  ClockIcon,
  UserIcon
} from '@heroicons/react/24/outline';

interface AppointmentCalendarProps {
  onSelectAppointment: (appointment: Appointment) => void;
  onCreateAppointment?: () => void;
}

export default function AppointmentCalendar({ onSelectAppointment, onCreateAppointment }: AppointmentCalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [view, setView] = useState<'day' | 'week' | 'month'>('day');

  // Use real data from Firebase
  const { data: appointments = [], isLoading, error } = useAppointments();

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-100 border-blue-300 text-blue-800';
      case 'confirmed': return 'bg-green-100 border-green-300 text-green-800';
      case 'in-progress': return 'bg-yellow-100 border-yellow-300 text-yellow-800';
      case 'completed': return 'bg-gray-100 border-gray-300 text-gray-800';
      case 'cancelled': return 'bg-red-100 border-red-300 text-red-800';
      default: return 'bg-blue-100 border-blue-300 text-blue-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'scheduled': return 'Terjadwal';
      case 'confirmed': return 'Dikonfirmasi';
      case 'in-progress': return 'Berlangsung';
      case 'completed': return 'Selesai';
      case 'cancelled': return 'Dibatalkan';
      default: return status;
    }
  };

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (view === 'day') {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
    } else if (view === 'week') {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
    } else {
      newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));
    }
    setCurrentDate(newDate);
  };

  const todayAppointments = appointments.filter(
    appointment => appointment.date === currentDate.toISOString().split('T')[0]
  );

  const timeSlots = [
    '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
    '13:00', '13:30', '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00'
  ];

  return (
    <div className="card">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <h2 className="text-xl font-semibold text-gray-900">Jadwal Appointment</h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => navigateDate('prev')}
              className="p-2 hover:bg-gray-100 rounded-lg"
            >
              <ChevronLeftIcon className="w-5 h-5" />
            </button>
            <span className="text-lg font-medium min-w-[200px] text-center">
              {formatDate(currentDate)}
            </span>
            <button
              onClick={() => navigateDate('next')}
              className="p-2 hover:bg-gray-100 rounded-lg"
            >
              <ChevronRightIcon className="w-5 h-5" />
            </button>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className="flex bg-gray-100 rounded-lg p-1">
            {(['day', 'week', 'month'] as const).map((viewType) => (
              <button
                key={viewType}
                onClick={() => setView(viewType)}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  view === viewType
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {viewType === 'day' ? 'Hari' : viewType === 'week' ? 'Minggu' : 'Bulan'}
              </button>
            ))}
          </div>
          <button
            onClick={onCreateAppointment}
            className="btn-primary"
          >
            + Buat Appointment
          </button>
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          <span className="ml-2 text-gray-600">Memuat jadwal appointment...</span>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <span className="text-red-800 font-medium">
              Error memuat jadwal: {error.message}
            </span>
          </div>
        </div>
      )}

      {/* Calendar View */}
      {!isLoading && !error && view === 'day' && (
        <div className="grid grid-cols-12 gap-4">
          {/* Time Column */}
          <div className="col-span-2">
            <div className="space-y-4">
              {timeSlots.map((time) => (
                <div key={time} className="h-16 flex items-center justify-end pr-4 text-sm text-gray-500">
                  {time}
                </div>
              ))}
            </div>
          </div>
          
          {/* Appointments Column */}
          <div className="col-span-10">
            <div className="space-y-4">
              {timeSlots.map((time) => {
                const appointment = todayAppointments.find(apt => apt.time === time);
                return (
                  <div key={time} className="h-16 border-l border-gray-200 pl-4 relative">
                    {appointment && (
                      <div
                        onClick={() => onSelectAppointment(appointment)}
                        className={`absolute inset-0 ml-4 p-3 rounded-lg border-l-4 cursor-pointer hover:shadow-md transition-shadow ${getStatusColor(appointment.status)}`}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-sm">{appointment.patientName}</p>
                            <p className="text-xs opacity-75">{appointment.type}</p>
                          </div>
                          <div className="text-right">
                            <p className="text-xs opacity-75">Dr. {appointment.doctorName}</p>
                            <div className="flex items-center text-xs opacity-75">
                              <ClockIcon className="w-3 h-3 mr-1" />
                              {appointment.duration}m
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}

      {/* List View for Week/Month */}
      {(view === 'week' || view === 'month') && (
        <div className="space-y-4">
          {todayAppointments.length > 0 ? (
            todayAppointments.map((appointment) => (
              <div
                key={appointment.id}
                onClick={() => onSelectAppointment(appointment)}
                className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors"
              >
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                      <UserIcon className="w-5 h-5 text-primary-600" />
                    </div>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{appointment.patientName}</p>
                    <p className="text-sm text-gray-600">{appointment.type}</p>
                    <p className="text-sm text-gray-500">Dr. {appointment.doctorName}</p>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="flex items-center text-sm text-gray-600 mb-2">
                    <ClockIcon className="w-4 h-4 mr-1" />
                    {appointment.time} ({appointment.duration} menit)
                  </div>
                  <span className={`status-badge ${getStatusColor(appointment.status).replace('bg-', 'status-').replace(' border-', ' ').replace(' text-', ' ')}`}>
                    {getStatusText(appointment.status)}
                  </span>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-gray-500">
              Tidak ada appointment pada tanggal ini
            </div>
          )}
        </div>
      )}
    </div>
  );
}
