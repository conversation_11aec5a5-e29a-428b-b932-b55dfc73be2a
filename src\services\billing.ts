import { 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  onSnapshot
} from 'firebase/firestore';
import { Invoice } from '@/types';
import { TenantService } from './base/TenantService';

export class BillingService extends TenantService {

  /**
   * Create new invoice
   */
  async createInvoice(invoiceData: Omit<Invoice, 'id'>): Promise<string> {
    try {
      this.validateTenantAccess();
      
      const newInvoice: Omit<Invoice, 'id'> = {
        ...invoiceData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      const docRef = await addDoc(this.getCollection('invoices'), newInvoice);
      return docRef.id;
    } catch (error) {
      this.handleError(error, 'create invoice');
    }
  }

  /**
   * Get invoice by ID
   */
  async getInvoice(id: string): Promise<Invoice | null> {
    try {
      this.validateTenantAccess();
      const docRef = this.getDocument('invoices', id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as Invoice;
      }
      return null;
    } catch (error) {
      this.handleError(error, 'get invoice');
    }
  }

  /**
   * Get all invoices for current tenant
   */
  async getInvoices(): Promise<Invoice[]> {
    try {
      this.validateTenantAccess();
      const q = query(
        this.getCollection('invoices'),
        orderBy('date', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Invoice[];
    } catch (error) {
      this.handleError(error, 'get invoices');
    }
  }

  /**
   * Get invoices by patient ID
   */
  async getInvoicesByPatient(patientId: string): Promise<Invoice[]> {
    try {
      this.validateTenantAccess();
      const q = query(
        this.getCollection('invoices'),
        where('patientId', '==', patientId),
        orderBy('date', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Invoice[];
    } catch (error) {
      this.handleError(error, 'get invoices by patient');
    }
  }

  /**
   * Get pending invoices
   */
  async getPendingInvoices(): Promise<Invoice[]> {
    try {
      this.validateTenantAccess();
      const q = query(
        this.getCollection('invoices'),
        where('status', 'in', ['sent', 'overdue']),
        orderBy('date', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Invoice[];
    } catch (error) {
      this.handleError(error, 'get pending invoices');
    }
  }

  /**
   * Update invoice
   */
  async updateInvoice(id: string, updates: Partial<Invoice>): Promise<void> {
    try {
      this.validateTenantAccess();
      const docRef = this.getDocument('invoices', id);
      await updateDoc(docRef, {
        ...updates,
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      this.handleError(error, 'update invoice');
    }
  }

  /**
   * Mark invoice as paid
   */
  async markAsPaid(id: string, paymentMethod: string): Promise<void> {
    try {
      this.validateTenantAccess();
      const docRef = this.getDocument('invoices', id);
      await updateDoc(docRef, {
        status: 'paid',
        paymentMethod,
        paidDate: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      this.handleError(error, 'mark invoice as paid');
    }
  }

  /**
   * Delete invoice
   */
  async deleteInvoice(id: string): Promise<void> {
    try {
      this.validateTenantAccess();
      const docRef = this.getDocument('invoices', id);
      await deleteDoc(docRef);
    } catch (error) {
      this.handleError(error, 'delete invoice');
    }
  }

  /**
   * Generate invoice number
   */
  async generateInvoiceNumber(): Promise<string> {
    try {
      this.validateTenantAccess();
      const today = new Date();
      const year = today.getFullYear();
      const month = String(today.getMonth() + 1).padStart(2, '0');
      
      // Get count of invoices this month
      const startOfMonth = new Date(year, today.getMonth(), 1);
      const endOfMonth = new Date(year, today.getMonth() + 1, 0);
      
      const q = query(
        this.getCollection('invoices'),
        where('date', '>=', startOfMonth.toISOString().split('T')[0]),
        where('date', '<=', endOfMonth.toISOString().split('T')[0])
      );
      
      const querySnapshot = await getDocs(q);
      const count = querySnapshot.size + 1;
      
      return `INV-${year}${month}-${String(count).padStart(4, '0')}`;
    } catch (error) {
      this.handleError(error, 'generate invoice number');
    }
  }

  /**
   * Calculate invoice totals
   */
  calculateTotals(items: Invoice['items'], discount: number = 0, taxRate: number = 0.11): {
    subtotal: number;
    tax: number;
    total: number;
  } {
    const subtotal = items.reduce((sum, item) => sum + (item.quantity * item.price), 0);
    const discountAmount = (subtotal * discount) / 100;
    const taxableAmount = subtotal - discountAmount;
    const tax = taxableAmount * taxRate;
    const total = taxableAmount + tax;

    return {
      subtotal,
      tax,
      total
    };
  }

  /**
   * Get revenue statistics
   */
  async getRevenueStats(startDate: string, endDate: string): Promise<{
    totalRevenue: number;
    paidInvoices: number;
    pendingInvoices: number;
    overdueInvoices: number;
  }> {
    try {
      this.validateTenantAccess();
      const q = query(
        this.getCollection('invoices'),
        where('date', '>=', startDate),
        where('date', '<=', endDate)
      );
      
      const querySnapshot = await getDocs(q);
      const invoices = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Invoice[];

      const totalRevenue = invoices
        .filter(inv => inv.status === 'paid')
        .reduce((sum, inv) => sum + inv.total, 0);

      const paidInvoices = invoices.filter(inv => inv.status === 'paid').length;
      const pendingInvoices = invoices.filter(inv => inv.status === 'sent').length;
      const overdueInvoices = invoices.filter(inv => inv.status === 'overdue').length;

      return {
        totalRevenue,
        paidInvoices,
        pendingInvoices,
        overdueInvoices
      };
    } catch (error) {
      this.handleError(error, 'get revenue stats');
    }
  }

  /**
   * Subscribe to invoices changes
   */
  subscribeToInvoices(callback: (invoices: Invoice[]) => void): () => void {
    this.validateTenantAccess();
    
    const q = query(
      this.getCollection('invoices'),
      orderBy('date', 'desc')
    );
    
    return onSnapshot(q, (snapshot) => {
      const invoices = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Invoice[];
      callback(invoices);
    });
  }

  /**
   * Create invoice from appointment
   */
  async createInvoiceFromAppointment(
    appointmentId: string,
    patientId: string,
    patientName: string,
    treatments: { name: string; price: number; quantity?: number }[]
  ): Promise<string> {
    try {
      this.validateTenantAccess();
      
      const invoiceNumber = await this.generateInvoiceNumber();
      const items = treatments.map(treatment => ({
        description: treatment.name,
        quantity: treatment.quantity || 1,
        price: treatment.price
      }));

      const { subtotal, tax, total } = this.calculateTotals(items);

      const invoiceData: Omit<Invoice, 'id'> = {
        invoiceNumber,
        patientId,
        patientName,
        date: new Date().toISOString().split('T')[0],
        items,
        subtotal,
        tax,
        discount: 0,
        total,
        status: 'draft',
        appointmentId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      return await this.createInvoice(invoiceData);
    } catch (error) {
      this.handleError(error, 'create invoice from appointment');
    }
  }
}
